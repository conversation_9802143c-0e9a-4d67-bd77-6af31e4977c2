# 陪诊师接单后预约成功短信功能

## 功能概述

在陪诊师确认接单后，系统会自动向预约用户发送预约成功的短信通知，告知用户陪诊服务的详细信息。

## 短信模板

**模板CODE**: `SMS_491470219`

**模板内容**:
```
尊敬的${username}您好，已成功为您预约${appointment_time} 医院：${hospital} ，科室：${apartment}的陪诊服务，陪诊人员姓名：${attendant_name}，电话：${attendant_mobile}，医院地址：${hospital_address}，请您携带相关证件及病历资料按时前往医院就诊，陪诊人员会在就诊前一日与您电话联系确认碰面时间，请注意接听电话。
```

## 模板参数说明

| 参数名 | 说明 | 示例 |
|--------|------|------|
| `username` | 用户姓名 | "张三" |
| `appointment_time` | 预约时间 | "2025年08月10日 14:30" |
| `hospital` | 医院名称 | "北京协和医院" |
| `apartment` | 科室名称 | "心内科" |
| `attendant_name` | 陪诊师姓名 | "李陪诊师" |
| `attendant_mobile` | 陪诊师手机号 | "13900139000" |
| `hospital_address` | 医院地址 | "北京市东城区东单帅府园1号" |

## 技术实现

### 1. 核心文件修改

#### 1.1 订单服务 (`backend/internal/service/impl/order.go`)

- **新增字段**: 在 `orderService` 结构中添加了 `smsService` 字段
- **修改构造函数**: `NewOrderService` 增加了 `smsService` 参数
- **新增方法**:
  - `sendAppointmentSuccessSMS`: 发送预约成功短信的主方法
  - `getOrderInfoForSMS`: 获取发送短信所需的订单信息
- **修改接单逻辑**: 在 `AcceptPendingOrder` 方法中，接单成功后异步发送短信

#### 1.2 服务初始化 (`backend/internal/app/setup.go`)

- **新增适配器**: `orderSMSAdapter` 结构体，适配短信服务接口
- **修改服务初始化**: 在两处 `NewOrderService` 调用中添加了 `smsService` 参数

### 2. 短信发送流程

```mermaid
sequenceDiagram
    participant A as 陪诊师
    participant B as 后端服务
    participant C as 短信服务
    participant D as 用户

    A->>B: 确认接单
    B->>B: 更新订单状态
    B->>B: 异步启动短信发送
    B->>A: 返回接单成功
    
    par 异步短信发送
        B->>B: 查询订单详细信息
        B->>B: 构建短信模板参数
        B->>C: 调用短信服务发送
        C->>D: 发送预约成功短信
    end
```

### 3. 数据查询逻辑

短信发送需要从多个表中查询信息：

```sql
SELECT 
    o.id as order_id,
    COALESCE(u.nickname, u.phone, '用户') as user_name,
    u.phone as user_phone,
    DATE_FORMAT(o.appointment_time, '%Y年%m月%d日 %H:%i') as appointment_time,
    COALESCE(a.hospital, '医院') as hospital,
    COALESCE(a.department, '科室') as department,
    COALESCE(att.name, '陪诊师') as attendant_name,
    COALESCE(att.phone, '') as attendant_phone,
    COALESCE(h.address, '医院地址') as hospital_address
FROM orders o
LEFT JOIN users u ON o.user_id = u.id
LEFT JOIN appointments a ON o.appointment_id = a.id
LEFT JOIN attendants att ON o.attendant_id = att.id
LEFT JOIN hospitals h ON a.hospital_id = h.id
WHERE o.id = ? AND o.deleted_at IS NULL
```

## 错误处理

1. **短信发送失败**: 不影响接单流程，只记录错误日志
2. **订单信息缺失**: 使用默认值填充，确保短信能够发送
3. **短信服务不可用**: 记录警告日志，不阻塞业务流程

## 配置要求

### 环境变量

```bash
SMS_ACCESS_KEY_ID=your_aliyun_access_key_id
SMS_ACCESS_KEY_SECRET=your_aliyun_access_key_secret
SMS_SIGN_NAME=your_sms_sign_name
```

### 配置文件 (`config/conf/config.prod.yaml`)

```yaml
sms:
  provider: "aliyun"
  aliyun:
    access_key_id: "${SMS_ACCESS_KEY_ID}"
    access_key_secret: "${SMS_ACCESS_KEY_SECRET}"
    endpoint: "dysmsapi.aliyuncs.com"
    region: "cn-hangzhou"
    sign_name: "${SMS_SIGN_NAME}"
```

## 测试方法

### 1. 单元测试

运行测试脚本验证短信发送功能：

```bash
cd backend
go run test_sms_integration.go
```

### 2. 集成测试

1. 创建一个测试订单
2. 让陪诊师接单
3. 检查用户是否收到预约成功短信

## 监控和日志

### 成功日志
```
预约成功短信发送成功: 订单ID=123, 用户手机=13800138000
```

### 失败日志
```
发送预约成功短信失败: 获取订单信息失败: record not found
发送预约成功短信失败: 发送短信失败: InvalidPhoneNumber
```

## 注意事项

1. **异步处理**: 短信发送采用异步方式，不会阻塞接单流程
2. **容错设计**: 短信发送失败不会影响订单状态
3. **数据完整性**: 使用 COALESCE 确保必要字段有默认值
4. **模板参数**: 注意模板中使用的是 `apartment` 而不是 `department`

## 后续优化建议

1. **重试机制**: 实现短信发送失败的重试逻辑
2. **发送记录**: 记录短信发送历史，便于追踪和统计
3. **模板管理**: 支持动态配置短信模板
4. **发送限制**: 实现短信发送频率限制，防止重复发送
