package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/handler"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.uber.org/zap"
)

// MockSettlementUploadService 模拟结算上传服务
type MockSettlementUploadService struct {
	mock.Mock
}

func (m *MockSettlementUploadService) UploadSettlementData(req *dto.SettlementUploadRequest, operatorID uint, operatorName string) (*dto.SettlementUploadResponse, error) {
	args := m.Called(req, operatorID, operatorName)
	return args.Get(0).(*dto.SettlementUploadResponse), args.Error(1)
}

func (m *MockSettlementUploadService) UploadPaymentRecords(req *dto.PaymentUploadRequest, operatorID uint, operatorName string) (*dto.PaymentUploadResponse, error) {
	args := m.Called(req, operatorID, operatorName)
	return args.Get(0).(*dto.PaymentUploadResponse), args.Error(1)
}

func (m *MockSettlementUploadService) ProcessFileUpload(file interface{}, req *dto.FileUploadRequest) (*dto.FileUploadResponse, error) {
	args := m.Called(file, req)
	return args.Get(0).(*dto.FileUploadResponse), args.Error(1)
}

func (m *MockSettlementUploadService) BatchProcess(req *dto.BatchProcessRequest, operatorID uint, operatorName string) (*dto.BatchProcessResponse, error) {
	args := m.Called(req, operatorID, operatorName)
	return args.Get(0).(*dto.BatchProcessResponse), args.Error(1)
}

func (m *MockSettlementUploadService) ValidateAttendantInfo(attendantID int64, name, phone string) error {
	args := m.Called(attendantID, name, phone)
	return args.Error(0)
}

func (m *MockSettlementUploadService) CheckSettlementExists(attendantID int64, settlementMonth string) (bool, error) {
	args := m.Called(attendantID, settlementMonth)
	return args.Bool(0), args.Error(1)
}

func TestSettlementUploadHandler_UploadSettlementData(t *testing.T) {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建模拟服务
	mockService := new(MockSettlementUploadService)
	logger := zap.NewNop()
	handler := handler.NewSettlementUploadHandler(mockService, logger)

	// 创建测试路由
	router := gin.New()
	router.POST("/upload/data", handler.UploadSettlementData)

	t.Run("成功上传结算数据", func(t *testing.T) {
		// 准备测试数据
		req := dto.SettlementUploadRequest{
			SettlementMonth: "2024-01",
			Records: []dto.SettlementUploadRecord{
				{
					AttendantID:    1,
					AttendantName:  "张三",
					AttendantPhone: "13800138000",
					BaseCommission: 1000.00,
					TaxAmount:      60.00,
					ActualAmount:   940.00,
					OrderCount:     10,
				},
			},
			OverwriteMode: false,
		}

		expectedResponse := &dto.SettlementUploadResponse{
			BatchID:         "SETTLEMENT_2024-01_12345678",
			SettlementMonth: "2024-01",
			TotalRecords:    1,
			SuccessCount:    1,
			FailedCount:     0,
			SuccessRecords: []dto.SettlementUploadResult{
				{
					SettlementID:   1,
					AttendantID:    1,
					AttendantName:  "张三",
					BaseCommission: 1000.00,
					TaxAmount:      60.00,
					ActualAmount:   940.00,
					OrderCount:     10,
					Status:         "success",
				},
			},
			FailedRecords: []dto.SettlementUploadFailedResult{},
			UploadTime:    time.Now(),
		}

		// 设置模拟服务期望
		mockService.On("UploadSettlementData", &req, uint(1), "system").Return(expectedResponse, nil)

		// 准备请求
		jsonData, _ := json.Marshal(req)
		request := httptest.NewRequest("POST", "/upload/data", bytes.NewBuffer(jsonData))
		request.Header.Set("Content-Type", "application/json")

		// 创建响应记录器
		recorder := httptest.NewRecorder()

		// 执行请求
		router.ServeHTTP(recorder, request)

		// 验证结果
		assert.Equal(t, http.StatusOK, recorder.Code)

		var response map[string]interface{}
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])
		assert.Equal(t, "结算数据上传完成", response["message"])

		// 验证模拟服务被正确调用
		mockService.AssertExpectations(t)
	})

	t.Run("请求参数错误", func(t *testing.T) {
		// 准备无效的请求数据
		invalidReq := map[string]interface{}{
			"settlement_month": "", // 空的结算月份
			"records":          []interface{}{},
		}

		jsonData, _ := json.Marshal(invalidReq)
		request := httptest.NewRequest("POST", "/upload/data", bytes.NewBuffer(jsonData))
		request.Header.Set("Content-Type", "application/json")

		recorder := httptest.NewRecorder()
		router.ServeHTTP(recorder, request)

		assert.Equal(t, http.StatusBadRequest, recorder.Code)

		var response map[string]interface{}
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(http.StatusBadRequest), response["code"])
	})

	t.Run("结算月份格式错误", func(t *testing.T) {
		req := dto.SettlementUploadRequest{
			SettlementMonth: "2024-13", // 无效月份
			Records: []dto.SettlementUploadRecord{
				{
					AttendantID:    1,
					BaseCommission: 1000.00,
					ActualAmount:   940.00,
				},
			},
		}

		jsonData, _ := json.Marshal(req)
		request := httptest.NewRequest("POST", "/upload/data", bytes.NewBuffer(jsonData))
		request.Header.Set("Content-Type", "application/json")

		recorder := httptest.NewRecorder()
		router.ServeHTTP(recorder, request)

		assert.Equal(t, http.StatusBadRequest, recorder.Code)

		var response map[string]interface{}
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(http.StatusBadRequest), response["code"])
		assert.Contains(t, response["message"], "结算月份格式错误")
	})

	t.Run("记录数量超过限制", func(t *testing.T) {
		// 创建超过1000条记录的请求
		records := make([]dto.SettlementUploadRecord, 1001)
		for i := 0; i < 1001; i++ {
			records[i] = dto.SettlementUploadRecord{
				AttendantID:    int64(i + 1),
				BaseCommission: 1000.00,
				ActualAmount:   940.00,
			}
		}

		req := dto.SettlementUploadRequest{
			SettlementMonth: "2024-01",
			Records:         records,
		}

		jsonData, _ := json.Marshal(req)
		request := httptest.NewRequest("POST", "/upload/data", bytes.NewBuffer(jsonData))
		request.Header.Set("Content-Type", "application/json")

		recorder := httptest.NewRecorder()
		router.ServeHTTP(recorder, request)

		assert.Equal(t, http.StatusBadRequest, recorder.Code)

		var response map[string]interface{}
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(http.StatusBadRequest), response["code"])
		assert.Contains(t, response["message"], "单次上传记录数量不能超过1000条")
	})
}

func TestSettlementUploadHandler_UploadPaymentRecords(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockSettlementUploadService)
	logger := zap.NewNop()
	handler := handler.NewSettlementUploadHandler(mockService, logger)

	router := gin.New()
	router.POST("/upload/payment", handler.UploadPaymentRecords)

	t.Run("成功上传打款记录", func(t *testing.T) {
		req := dto.PaymentUploadRequest{
			BatchNo:     "PAY_2024_001",
			PaymentTime: time.Now(),
			Records: []dto.PaymentUploadRecord{
				{
					SettlementID:    1,
					PaymentAmount:   940.00,
					AttendantID:     1,
					AttendantName:   "张三",
					SettlementMonth: "2024-01",
					Remark:          "正常打款",
				},
			},
		}

		expectedResponse := &dto.PaymentUploadResponse{
			BatchNo:      "PAY_2024_001",
			PaymentTime:  req.PaymentTime,
			TotalRecords: 1,
			SuccessCount: 1,
			FailedCount:  0,
			SuccessRecords: []dto.PaymentUploadResult{
				{
					PaymentID:     1,
					SettlementID:  1,
					AttendantID:   1,
					AttendantName: "张三",
					PaymentAmount: 940.00,
					PaymentTime:   req.PaymentTime,
					Status:        "success",
				},
			},
			FailedRecords: []dto.PaymentUploadFailedResult{},
			UploadTime:    time.Now(),
		}

		mockService.On("UploadPaymentRecords", &req, uint(1), "system").Return(expectedResponse, nil)

		jsonData, _ := json.Marshal(req)
		request := httptest.NewRequest("POST", "/upload/payment", bytes.NewBuffer(jsonData))
		request.Header.Set("Content-Type", "application/json")

		recorder := httptest.NewRecorder()
		router.ServeHTTP(recorder, request)

		assert.Equal(t, http.StatusOK, recorder.Code)

		var response map[string]interface{}
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])
		assert.Equal(t, "打款记录上传完成", response["message"])

		mockService.AssertExpectations(t)
	})
}

func TestSettlementUploadHandler_ValidateAttendant(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockSettlementUploadService)
	logger := zap.NewNop()
	handler := handler.NewSettlementUploadHandler(mockService, logger)

	router := gin.New()
	router.GET("/validate-attendant", handler.ValidateAttendant)

	t.Run("验证陪诊师信息成功", func(t *testing.T) {
		mockService.On("ValidateAttendantInfo", int64(1), "张三", "13800138000").Return(nil)

		request := httptest.NewRequest("GET", "/validate-attendant?attendant_id=1&name=张三&phone=13800138000", nil)
		recorder := httptest.NewRecorder()

		router.ServeHTTP(recorder, request)

		assert.Equal(t, http.StatusOK, recorder.Code)

		var response map[string]interface{}
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])
		assert.Equal(t, "陪诊师信息验证通过", response["message"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(1), data["attendant_id"])
		assert.Equal(t, "张三", data["name"])
		assert.Equal(t, "13800138000", data["phone"])
		assert.Equal(t, true, data["valid"])

		mockService.AssertExpectations(t)
	})

	t.Run("陪诊师ID为空", func(t *testing.T) {
		request := httptest.NewRequest("GET", "/validate-attendant", nil)
		recorder := httptest.NewRecorder()

		router.ServeHTTP(recorder, request)

		assert.Equal(t, http.StatusBadRequest, recorder.Code)

		var response map[string]interface{}
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(http.StatusBadRequest), response["code"])
		assert.Contains(t, response["message"], "陪诊师ID不能为空")
	})
}

func TestSettlementUploadHandler_CheckSettlementExists(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockSettlementUploadService)
	logger := zap.NewNop()
	handler := handler.NewSettlementUploadHandler(mockService, logger)

	router := gin.New()
	router.GET("/check-exists", handler.CheckSettlementExists)

	t.Run("检查结算记录存在", func(t *testing.T) {
		mockService.On("CheckSettlementExists", int64(1), "2024-01").Return(true, nil)

		request := httptest.NewRequest("GET", "/check-exists?attendant_id=1&settlement_month=2024-01", nil)
		recorder := httptest.NewRecorder()

		router.ServeHTTP(recorder, request)

		assert.Equal(t, http.StatusOK, recorder.Code)

		var response map[string]interface{}
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(0), response["code"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(1), data["attendant_id"])
		assert.Equal(t, "2024-01", data["settlement_month"])
		assert.Equal(t, true, data["exists"])

		mockService.AssertExpectations(t)
	})

	t.Run("结算月份格式错误", func(t *testing.T) {
		request := httptest.NewRequest("GET", "/check-exists?attendant_id=1&settlement_month=2024-13", nil)
		recorder := httptest.NewRecorder()

		router.ServeHTTP(recorder, request)

		assert.Equal(t, http.StatusBadRequest, recorder.Code)

		var response map[string]interface{}
		err := json.Unmarshal(recorder.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(http.StatusBadRequest), response["code"])
		assert.Contains(t, response["message"], "结算月份格式错误")
	})
}
