package integration

import (
	"testing"
	"time"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// SettlementUploadIntegrationTestSuite 结算上传集成测试套件
type SettlementUploadIntegrationTestSuite struct {
	suite.Suite
	db      *gorm.DB
	service service.ISettlementUploadService
	logger  *zap.Logger
}

// SetupSuite 设置测试套件
func (suite *SettlementUploadIntegrationTestSuite) SetupSuite() {
	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// 创建测试表
	err = db.Exec(`
		CREATE TABLE settlement_records (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			attendant_id INTEGER NOT NULL,
			settlement_month VARCHAR(7) NOT NULL,
			base_commission DECIMAL(10,2) NOT NULL,
			tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
			actual_amount DECIMAL(10,2) NOT NULL,
			settlement_status VARCHAR(20) NOT NULL DEFAULT 'pending',
			order_count INTEGER NOT NULL DEFAULT 0,
			upload_batch_id VARCHAR(50),
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			deleted_at DATETIME,
			UNIQUE(attendant_id, settlement_month)
		)
	`).Error
	suite.Require().NoError(err)

	err = db.Exec(`
		CREATE TABLE payment_records (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			settlement_id INTEGER NOT NULL,
			batch_no VARCHAR(100) NOT NULL,
			payment_amount DECIMAL(10,2) NOT NULL,
			payment_time DATETIME NOT NULL,
			payment_status VARCHAR(20) NOT NULL DEFAULT 'pending',
			remark TEXT,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			deleted_at DATETIME
		)
	`).Error
	suite.Require().NoError(err)

	err = db.Exec(`
		CREATE TABLE attendants (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			user_id INTEGER NOT NULL,
			name VARCHAR(50) NOT NULL,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			deleted_at DATETIME
		)
	`).Error
	suite.Require().NoError(err)

	err = db.Exec(`
		CREATE TABLE users (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			phone VARCHAR(20) NOT NULL,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			deleted_at DATETIME
		)
	`).Error
	suite.Require().NoError(err)

	err = db.Exec(`
		CREATE TABLE operation_logs (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			operation_type VARCHAR(50) NOT NULL,
			target_type VARCHAR(50) NOT NULL,
			target_id INTEGER NOT NULL,
			operator_id INTEGER NOT NULL,
			operator_name VARCHAR(100) NOT NULL,
			remark TEXT,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`).Error
	suite.Require().NoError(err)

	// 插入测试数据
	err = db.Exec(`
		INSERT INTO users (id, phone) VALUES (1, '13800138000'), (2, '13800138001')
	`).Error
	suite.Require().NoError(err)

	err = db.Exec(`
		INSERT INTO attendants (user_id, name) VALUES (1, '张三'), (2, '李四')
	`).Error
	suite.Require().NoError(err)

	suite.db = db
	suite.logger = zap.NewNop()
	suite.service = service.NewSettlementUploadService(db, suite.logger)
}

// TearDownSuite 清理测试套件
func (suite *SettlementUploadIntegrationTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

// TestUploadSettlementData 测试上传结算数据
func (suite *SettlementUploadIntegrationTestSuite) TestUploadSettlementData() {
	req := &dto.SettlementUploadRequest{
		SettlementMonth: "2024-01",
		Records: []dto.SettlementUploadRecord{
			{
				AttendantID:    1,
				AttendantName:  "张三",
				AttendantPhone: "13800138000",
				BaseCommission: 1000.00,
				TaxAmount:      60.00,
				ActualAmount:   940.00,
				OrderCount:     10,
			},
			{
				AttendantID:    2,
				AttendantName:  "李四",
				AttendantPhone: "13800138001",
				BaseCommission: 800.00,
				TaxAmount:      48.00,
				ActualAmount:   752.00,
				OrderCount:     8,
			},
		},
		OverwriteMode: false,
	}

	response, err := suite.service.UploadSettlementData(req, 1, "admin")

	suite.NoError(err)
	suite.NotNil(response)
	suite.Equal("2024-01", response.SettlementMonth)
	suite.Equal(2, response.TotalRecords)
	suite.Equal(2, response.SuccessCount)
	suite.Equal(0, response.FailedCount)
	suite.Len(response.SuccessRecords, 2)
	suite.Len(response.FailedRecords, 0)

	// 验证数据库中的记录
	var count int64
	err = suite.db.Raw("SELECT COUNT(*) FROM settlement_records WHERE settlement_month = ?", "2024-01").Scan(&count).Error
	suite.NoError(err)
	suite.Equal(int64(2), count)

	// 验证具体记录
	var record struct {
		AttendantID      int64   `json:"attendant_id"`
		BaseCommission   float64 `json:"base_commission"`
		TaxAmount        float64 `json:"tax_amount"`
		ActualAmount     float64 `json:"actual_amount"`
		OrderCount       int     `json:"order_count"`
		SettlementStatus string  `json:"settlement_status"`
	}
	err = suite.db.Raw(`
		SELECT attendant_id, base_commission, tax_amount, actual_amount, order_count, settlement_status
		FROM settlement_records 
		WHERE attendant_id = 1 AND settlement_month = '2024-01'
	`).Scan(&record).Error
	suite.NoError(err)
	suite.Equal(int64(1), record.AttendantID)
	suite.Equal(1000.00, record.BaseCommission)
	suite.Equal(60.00, record.TaxAmount)
	suite.Equal(940.00, record.ActualAmount)
	suite.Equal(10, record.OrderCount)
	suite.Equal("calculated", record.SettlementStatus)
}

// TestUploadSettlementDataWithOverwrite 测试覆盖模式上传结算数据
func (suite *SettlementUploadIntegrationTestSuite) TestUploadSettlementDataWithOverwrite() {
	// 先插入一条记录
	err := suite.db.Exec(`
		INSERT INTO settlement_records (attendant_id, settlement_month, base_commission, tax_amount, actual_amount, order_count, settlement_status)
		VALUES (1, '2024-02', 500.00, 30.00, 470.00, 5, 'calculated')
	`).Error
	suite.NoError(err)

	// 使用覆盖模式上传新数据
	req := &dto.SettlementUploadRequest{
		SettlementMonth: "2024-02",
		Records: []dto.SettlementUploadRecord{
			{
				AttendantID:    1,
				AttendantName:  "张三",
				AttendantPhone: "13800138000",
				BaseCommission: 1200.00,
				TaxAmount:      72.00,
				ActualAmount:   1128.00,
				OrderCount:     12,
			},
		},
		OverwriteMode: true,
	}

	response, err := suite.service.UploadSettlementData(req, 1, "admin")

	suite.NoError(err)
	suite.NotNil(response)
	suite.Equal(1, response.SuccessCount)
	suite.Equal(0, response.FailedCount)

	// 验证记录被更新
	var record struct {
		BaseCommission float64 `json:"base_commission"`
		TaxAmount      float64 `json:"tax_amount"`
		ActualAmount   float64 `json:"actual_amount"`
		OrderCount     int     `json:"order_count"`
	}
	err = suite.db.Raw(`
		SELECT base_commission, tax_amount, actual_amount, order_count
		FROM settlement_records 
		WHERE attendant_id = 1 AND settlement_month = '2024-02'
	`).Scan(&record).Error
	suite.NoError(err)
	suite.Equal(1200.00, record.BaseCommission)
	suite.Equal(72.00, record.TaxAmount)
	suite.Equal(1128.00, record.ActualAmount)
	suite.Equal(12, record.OrderCount)
}

// TestUploadPaymentRecords 测试上传打款记录
func (suite *SettlementUploadIntegrationTestSuite) TestUploadPaymentRecords() {
	// 先插入结算记录
	err := suite.db.Exec(`
		INSERT INTO settlement_records (id, attendant_id, settlement_month, base_commission, tax_amount, actual_amount, order_count, settlement_status)
		VALUES (1, 1, '2024-03', 1000.00, 60.00, 940.00, 10, 'calculated')
	`).Error
	suite.NoError(err)

	paymentTime := time.Now()
	req := &dto.PaymentUploadRequest{
		BatchNo:     "PAY_2024_001",
		PaymentTime: paymentTime,
		Records: []dto.PaymentUploadRecord{
			{
				SettlementID:    1,
				PaymentAmount:   940.00,
				AttendantID:     1,
				AttendantName:   "张三",
				SettlementMonth: "2024-03",
				Remark:          "正常打款",
			},
		},
	}

	response, err := suite.service.UploadPaymentRecords(req, 1, "admin")

	suite.NoError(err)
	suite.NotNil(response)
	suite.Equal("PAY_2024_001", response.BatchNo)
	suite.Equal(1, response.TotalRecords)
	suite.Equal(1, response.SuccessCount)
	suite.Equal(0, response.FailedCount)
	suite.Len(response.SuccessRecords, 1)

	// 验证打款记录
	var paymentRecord struct {
		SettlementID  int64   `json:"settlement_id"`
		BatchNo       string  `json:"batch_no"`
		PaymentAmount float64 `json:"payment_amount"`
		PaymentStatus string  `json:"payment_status"`
		Remark        string  `json:"remark"`
	}
	err = suite.db.Raw(`
		SELECT settlement_id, batch_no, payment_amount, payment_status, remark
		FROM payment_records 
		WHERE batch_no = 'PAY_2024_001'
	`).Scan(&paymentRecord).Error
	suite.NoError(err)
	suite.Equal(int64(1), paymentRecord.SettlementID)
	suite.Equal("PAY_2024_001", paymentRecord.BatchNo)
	suite.Equal(940.00, paymentRecord.PaymentAmount)
	suite.Equal("success", paymentRecord.PaymentStatus)
	suite.Equal("正常打款", paymentRecord.Remark)

	// 验证结算记录状态被更新为已打款
	var settlementStatus string
	err = suite.db.Raw(`
		SELECT settlement_status FROM settlement_records WHERE id = 1
	`).Scan(&settlementStatus).Error
	suite.NoError(err)
	suite.Equal("paid", settlementStatus)
}

// TestValidateAttendantInfo 测试验证陪诊师信息
func (suite *SettlementUploadIntegrationTestSuite) TestValidateAttendantInfo() {
	// 测试有效的陪诊师信息
	err := suite.service.ValidateAttendantInfo(1, "张三", "13800138000")
	suite.NoError(err)

	// 测试不存在的陪诊师
	err = suite.service.ValidateAttendantInfo(999, "", "")
	suite.Error(err)
	suite.Contains(err.Error(), "陪诊师 999 不存在")

	// 测试姓名不匹配
	err = suite.service.ValidateAttendantInfo(1, "王五", "")
	suite.Error(err)
	suite.Contains(err.Error(), "陪诊师姓名不匹配")

	// 测试手机号不匹配
	err = suite.service.ValidateAttendantInfo(1, "", "13800138999")
	suite.Error(err)
	suite.Contains(err.Error(), "陪诊师手机号不匹配")
}

// TestCheckSettlementExists 测试检查结算记录是否存在
func (suite *SettlementUploadIntegrationTestSuite) TestCheckSettlementExists() {
	// 插入测试记录
	err := suite.db.Exec(`
		INSERT INTO settlement_records (attendant_id, settlement_month, base_commission, tax_amount, actual_amount, order_count, settlement_status)
		VALUES (1, '2024-04', 1000.00, 60.00, 940.00, 10, 'calculated')
	`).Error
	suite.NoError(err)

	// 测试存在的记录
	exists, err := suite.service.CheckSettlementExists(1, "2024-04")
	suite.NoError(err)
	suite.True(exists)

	// 测试不存在的记录
	exists, err = suite.service.CheckSettlementExists(1, "2024-05")
	suite.NoError(err)
	suite.False(exists)

	// 测试不存在的陪诊师
	exists, err = suite.service.CheckSettlementExists(999, "2024-04")
	suite.NoError(err)
	suite.False(exists)
}

// TestUploadSettlementDataValidationErrors 测试上传结算数据验证错误
func (suite *SettlementUploadIntegrationTestSuite) TestUploadSettlementDataValidationErrors() {
	req := &dto.SettlementUploadRequest{
		SettlementMonth: "2024-05",
		Records: []dto.SettlementUploadRecord{
			{
				AttendantID:    999, // 不存在的陪诊师
				AttendantName:  "不存在",
				AttendantPhone: "13800138999",
				BaseCommission: 1000.00,
				TaxAmount:      60.00,
				ActualAmount:   940.00,
				OrderCount:     10,
			},
			{
				AttendantID:    1, // 存在的陪诊师，但姓名不匹配
				AttendantName:  "错误姓名",
				AttendantPhone: "13800138000",
				BaseCommission: 800.00,
				TaxAmount:      48.00,
				ActualAmount:   752.00,
				OrderCount:     8,
			},
		},
		OverwriteMode: false,
	}

	response, err := suite.service.UploadSettlementData(req, 1, "admin")

	suite.NoError(err)
	suite.NotNil(response)
	suite.Equal(2, response.TotalRecords)
	suite.Equal(0, response.SuccessCount)
	suite.Equal(2, response.FailedCount)
	suite.Len(response.SuccessRecords, 0)
	suite.Len(response.FailedRecords, 2)

	// 验证错误信息
	suite.Contains(response.FailedRecords[0].ErrorMessage, "陪诊师 999 不存在")
	suite.Contains(response.FailedRecords[1].ErrorMessage, "陪诊师姓名不匹配")
}

// 运行测试套件
func TestSettlementUploadIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(SettlementUploadIntegrationTestSuite))
}
