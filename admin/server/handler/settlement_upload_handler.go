package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// SettlementUploadHandler 结算数据上传处理器
type SettlementUploadHandler struct {
	uploadService service.ISettlementUploadService
	logger        *zap.Logger
}

// NewSettlementUploadHandler 创建结算数据上传处理器
func NewSettlementUploadHandler(uploadService service.ISettlementUploadService, logger *zap.Logger) *SettlementUploadHandler {
	return &SettlementUploadHandler{
		uploadService: uploadService,
		logger:        logger,
	}
}

// UploadSettlementData 上传结算数据
// @Summary 上传结算数据
// @Tags 结算管理
// @Accept json
// @Produce json
// @Param request body dto.SettlementUploadRequest true "结算数据上传请求"
// @Success 200 {object} dto.SettlementUploadResponse
// @Router /admin/settlement/upload/data [post]
func (h *SettlementUploadHandler) UploadSettlementData(c *gin.Context) {
	var req dto.SettlementUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定结算数据上传请求失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证结算月份格式
	if _, err := time.Parse("2006-01", req.SettlementMonth); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "结算月份格式错误，应为 YYYY-MM 格式",
		})
		return
	}

	// 验证记录数量限制
	if len(req.Records) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "结算记录不能为空",
		})
		return
	}

	if len(req.Records) > 1000 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "单次上传记录数量不能超过1000条",
		})
		return
	}

	// 获取操作人信息
	operatorID, operatorName := h.getOperatorFromContext(c)

	// 调用服务处理上传
	response, err := h.uploadService.UploadSettlementData(&req, operatorID, operatorName)
	if err != nil {
		h.logger.Error("上传结算数据失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "上传结算数据失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("结算数据上传成功",
		zap.String("batch_id", response.BatchID),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failed_count", response.FailedCount),
		zap.String("operator", operatorName))

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "结算数据上传完成",
		"data":    response,
	})
}

// UploadPaymentRecords 上传打款记录
// @Summary 上传打款记录
// @Tags 结算管理
// @Accept json
// @Produce json
// @Param request body dto.PaymentUploadRequest true "打款记录上传请求"
// @Success 200 {object} dto.PaymentUploadResponse
// @Router /admin/settlement/upload/payment [post]
func (h *SettlementUploadHandler) UploadPaymentRecords(c *gin.Context) {
	var req dto.PaymentUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定打款记录上传请求失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证记录数量限制
	if len(req.Records) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "打款记录不能为空",
		})
		return
	}

	if len(req.Records) > 1000 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "单次上传记录数量不能超过1000条",
		})
		return
	}

	// 验证批次号唯一性
	// TODO: 可以添加批次号重复检查逻辑

	// 获取操作人信息
	operatorID, operatorName := h.getOperatorFromContext(c)

	// 调用服务处理上传
	response, err := h.uploadService.UploadPaymentRecords(&req, operatorID, operatorName)
	if err != nil {
		h.logger.Error("上传打款记录失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "上传打款记录失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("打款记录上传成功",
		zap.String("batch_no", response.BatchNo),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failed_count", response.FailedCount),
		zap.String("operator", operatorName))

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "打款记录上传完成",
		"data":    response,
	})
}

// UploadFile 文件上传处理
// @Summary 文件上传处理
// @Tags 结算管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "上传文件"
// @Param upload_type formData string true "上传类型" Enums(settlement, payment)
// @Param settlement_month formData string false "结算月份"
// @Param batch_no formData string false "批次号"
// @Param payment_time formData string false "打款时间"
// @Param overwrite_mode formData bool false "是否覆盖已存在记录"
// @Param remark formData string false "备注"
// @Success 200 {object} dto.FileUploadResponse
// @Router /admin/settlement/upload/file [post]
func (h *SettlementUploadHandler) UploadFile(c *gin.Context) {
	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "获取上传文件失败",
			"error":   err.Error(),
		})
		return
	}

	// 绑定表单参数
	var req dto.FileUploadRequest
	if err := c.ShouldBind(&req); err != nil {
		h.logger.Error("绑定文件上传请求失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证文件大小（限制为10MB）
	if file.Size > 10*1024*1024 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "文件大小不能超过10MB",
		})
		return
	}

	// 根据上传类型验证必填参数
	if req.UploadType == "settlement" && req.SettlementMonth == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "结算数据上传时结算月份不能为空",
		})
		return
	}

	if req.UploadType == "payment" {
		if req.BatchNo == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    http.StatusBadRequest,
				"message": "打款记录上传时批次号不能为空",
			})
			return
		}
		if req.PaymentTime == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    http.StatusBadRequest,
				"message": "打款记录上传时打款时间不能为空",
			})
			return
		}
	}

	// 调用服务处理文件上传
	response, err := h.uploadService.ProcessFileUpload(file, &req)
	if err != nil {
		h.logger.Error("处理文件上传失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "处理文件上传失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("文件上传处理完成",
		zap.String("filename", file.Filename),
		zap.String("upload_type", req.UploadType),
		zap.Int("valid_rows", response.ValidRows),
		zap.Int("invalid_rows", response.InvalidRows))

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "文件上传处理完成",
		"data":    response,
	})
}

// BatchProcess 批量处理（确认/回滚）
// @Summary 批量处理（确认/回滚）
// @Tags 结算管理
// @Accept json
// @Produce json
// @Param request body dto.BatchProcessRequest true "批量处理请求"
// @Success 200 {object} dto.BatchProcessResponse
// @Router /admin/settlement/upload/batch-process [post]
func (h *SettlementUploadHandler) BatchProcess(c *gin.Context) {
	var req dto.BatchProcessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("绑定批量处理请求失败", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取操作人信息
	operatorID, operatorName := h.getOperatorFromContext(c)

	// 调用服务处理批量操作
	response, err := h.uploadService.BatchProcess(&req, operatorID, operatorName)
	if err != nil {
		h.logger.Error("批量处理失败", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "批量处理失败",
			"error":   err.Error(),
		})
		return
	}

	h.logger.Info("批量处理完成",
		zap.String("process_type", req.ProcessType),
		zap.String("batch_id", req.BatchID),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failed_count", response.FailedCount),
		zap.String("operator", operatorName))

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "批量处理完成",
		"data":    response,
	})
}

// ValidateAttendant 验证陪诊师信息
// @Summary 验证陪诊师信息
// @Tags 结算管理
// @Accept json
// @Produce json
// @Param attendant_id query int true "陪诊师ID"
// @Param name query string false "陪诊师姓名"
// @Param phone query string false "陪诊师手机号"
// @Success 200 {object} map[string]interface{}
// @Router /admin/settlement/upload/validate-attendant [get]
func (h *SettlementUploadHandler) ValidateAttendant(c *gin.Context) {
	attendantIDStr := c.Query("attendant_id")
	if attendantIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "陪诊师ID不能为空",
		})
		return
	}

	attendantID, err := strconv.ParseInt(attendantIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "陪诊师ID格式错误",
		})
		return
	}

	name := c.Query("name")
	phone := c.Query("phone")

	// 调用服务验证陪诊师信息
	err = h.uploadService.ValidateAttendantInfo(attendantID, name, phone)
	if err != nil {
		h.logger.Warn("陪诊师信息验证失败",
			zap.Int64("attendant_id", attendantID),
			zap.String("name", name),
			zap.String("phone", phone),
			zap.Error(err))

		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "陪诊师信息验证失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "陪诊师信息验证通过",
		"data": gin.H{
			"attendant_id": attendantID,
			"name":         name,
			"phone":        phone,
			"valid":        true,
		},
	})
}

// CheckSettlementExists 检查结算记录是否存在
// @Summary 检查结算记录是否存在
// @Tags 结算管理
// @Accept json
// @Produce json
// @Param attendant_id query int true "陪诊师ID"
// @Param settlement_month query string true "结算月份"
// @Success 200 {object} map[string]interface{}
// @Router /admin/settlement/upload/check-exists [get]
func (h *SettlementUploadHandler) CheckSettlementExists(c *gin.Context) {
	attendantIDStr := c.Query("attendant_id")
	if attendantIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "陪诊师ID不能为空",
		})
		return
	}

	attendantID, err := strconv.ParseInt(attendantIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "陪诊师ID格式错误",
		})
		return
	}

	settlementMonth := c.Query("settlement_month")
	if settlementMonth == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "结算月份不能为空",
		})
		return
	}

	// 验证结算月份格式
	if _, err := time.Parse("2006-01", settlementMonth); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "结算月份格式错误，应为 YYYY-MM 格式",
		})
		return
	}

	// 调用服务检查结算记录是否存在
	exists, err := h.uploadService.CheckSettlementExists(attendantID, settlementMonth)
	if err != nil {
		h.logger.Error("检查结算记录失败",
			zap.Int64("attendant_id", attendantID),
			zap.String("settlement_month", settlementMonth),
			zap.Error(err))

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    http.StatusInternalServerError,
			"message": "检查结算记录失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "检查完成",
		"data": gin.H{
			"attendant_id":     attendantID,
			"settlement_month": settlementMonth,
			"exists":           exists,
		},
	})
}

// GetUploadHistory 获取上传历史记录
// @Summary 获取上传历史记录
// @Tags 结算管理
// @Accept json
// @Produce json
// @Param upload_type query string false "上传类型" Enums(settlement, payment)
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Success 200 {object} map[string]interface{}
// @Router /admin/settlement/upload/history [get]
func (h *SettlementUploadHandler) GetUploadHistory(c *gin.Context) {
	var filter struct {
		UploadType string `form:"upload_type"`
		StartDate  string `form:"start_date"`
		EndDate    string `form:"end_date"`
		Page       int    `form:"page"`
		PageSize   int    `form:"page_size"`
	}

	if err := c.ShouldBindQuery(&filter); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    http.StatusBadRequest,
			"message": "参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 设置默认分页参数
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.PageSize <= 0 {
		filter.PageSize = 20
	}

	// TODO: 实现上传历史记录查询逻辑
	// 这里可以查询 operation_logs 表或者专门的上传历史表

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取成功",
		"data": gin.H{
			"list":  []interface{}{},
			"total": 0,
			"page":  filter.Page,
			"limit": filter.PageSize,
		},
	})
}

// getOperatorFromContext 从上下文获取操作人信息
func (h *SettlementUploadHandler) getOperatorFromContext(c *gin.Context) (uint, string) {
	// 从JWT中间件设置的上下文获取管理员ID
	adminIDValue, exists := c.Get("admin_id")
	if !exists {
		h.logger.Warn("无法从上下文获取管理员ID，使用默认值")
		return 1, "system"
	}

	adminID, ok := adminIDValue.(uint)
	if !ok {
		h.logger.Warn("管理员ID类型断言失败，使用默认值")
		return 1, "system"
	}

	// 从上下文获取管理员用户名
	usernameValue, exists := c.Get("admin_name")
	if !exists {
		return adminID, "admin"
	}

	username, ok := usernameValue.(string)
	if !ok {
		return adminID, "admin"
	}

	return adminID, username
}
