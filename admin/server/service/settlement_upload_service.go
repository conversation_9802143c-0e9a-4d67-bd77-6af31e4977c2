package service

import (
	"encoding/csv"
	"fmt"
	"io"
	"mime/multipart"
	"strconv"
	"strings"
	"time"

	"github.com/gemeijie/peizhen/admin/server/dto"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ISettlementUploadService 结算数据上传服务接口
type ISettlementUploadService interface {
	// 上传结算数据
	UploadSettlementData(req *dto.SettlementUploadRequest, operatorID uint, operatorName string) (*dto.SettlementUploadResponse, error)

	// 上传打款记录
	UploadPaymentRecords(req *dto.PaymentUploadRequest, operatorID uint, operatorName string) (*dto.PaymentUploadResponse, error)

	// 处理文件上传
	ProcessFileUpload(file *multipart.FileHeader, req *dto.FileUploadRequest) (*dto.FileUploadResponse, error)

	// 批量处理确认/回滚
	BatchProcess(req *dto.BatchProcessRequest, operatorID uint, operatorName string) (*dto.BatchProcessResponse, error)

	// 验证陪诊师信息
	ValidateAttendantInfo(attendantID int64, name, phone string) error

	// 检查结算记录是否存在
	CheckSettlementExists(attendantID int64, settlementMonth string) (bool, error)
}

// SettlementUploadService 结算数据上传服务实现
type SettlementUploadService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewSettlementUploadService 创建结算数据上传服务
func NewSettlementUploadService(db *gorm.DB, logger *zap.Logger) ISettlementUploadService {
	return &SettlementUploadService{
		db:     db,
		logger: logger,
	}
}

// UploadSettlementData 上传结算数据
func (s *SettlementUploadService) UploadSettlementData(req *dto.SettlementUploadRequest, operatorID uint, operatorName string) (*dto.SettlementUploadResponse, error) {
	s.logger.Info("开始上传结算数据",
		zap.String("settlement_month", req.SettlementMonth),
		zap.Int("record_count", len(req.Records)),
		zap.String("operator", operatorName))

	// 生成批次ID
	batchID := fmt.Sprintf("SETTLEMENT_%s_%s", req.SettlementMonth, uuid.New().String()[:8])

	response := &dto.SettlementUploadResponse{
		BatchID:         batchID,
		SettlementMonth: req.SettlementMonth,
		TotalRecords:    len(req.Records),
		SuccessRecords:  make([]dto.SettlementUploadResult, 0),
		FailedRecords:   make([]dto.SettlementUploadFailedResult, 0),
		UploadTime:      time.Now(),
	}

	// 开启事务处理
	err := s.db.Transaction(func(tx *gorm.DB) error {
		for _, record := range req.Records {
			result, err := s.processSettlementRecord(tx, &record, req.SettlementMonth, batchID, req.OverwriteMode, operatorID, operatorName)
			if err != nil {
				// 记录失败
				failedResult := dto.SettlementUploadFailedResult{
					AttendantID:   record.AttendantID,
					AttendantName: record.AttendantName,
					ErrorCode:     "PROCESS_ERROR",
					ErrorMessage:  err.Error(),
				}
				response.FailedRecords = append(response.FailedRecords, failedResult)
				response.FailedCount++
			} else {
				// 记录成功
				response.SuccessRecords = append(response.SuccessRecords, *result)
				response.SuccessCount++
			}
		}
		return nil
	})

	if err != nil {
		s.logger.Error("上传结算数据事务失败", zap.Error(err))
		return nil, fmt.Errorf("上传结算数据失败: %v", err)
	}

	s.logger.Info("结算数据上传完成",
		zap.String("batch_id", batchID),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failed_count", response.FailedCount))

	return response, nil
}

// processSettlementRecord 处理单条结算记录
func (s *SettlementUploadService) processSettlementRecord(tx *gorm.DB, record *dto.SettlementUploadRecord, settlementMonth, batchID string, overwriteMode bool, operatorID uint, operatorName string) (*dto.SettlementUploadResult, error) {
	// 验证陪诊师信息
	if err := s.validateAttendantInfoInTx(tx, record.AttendantID, record.AttendantName, record.AttendantPhone); err != nil {
		return nil, fmt.Errorf("陪诊师信息验证失败: %v", err)
	}

	// 检查是否已存在结算记录
	exists, existingID, err := s.checkSettlementExistsInTx(tx, record.AttendantID, settlementMonth)
	if err != nil {
		return nil, fmt.Errorf("检查结算记录失败: %v", err)
	}

	if exists && !overwriteMode {
		return nil, fmt.Errorf("陪诊师 %d 在 %s 月份的结算记录已存在", record.AttendantID, settlementMonth)
	}

	// 获取陪诊师姓名
	var attendantName string
	err = tx.Raw(`
		SELECT a.name 
		FROM attendants a 
		WHERE a.user_id = ? AND a.deleted_at IS NULL
	`, record.AttendantID).Scan(&attendantName).Error
	if err != nil {
		return nil, fmt.Errorf("获取陪诊师姓名失败: %v", err)
	}

	var settlementID int64
	now := time.Now()

	if exists && overwriteMode {
		// 更新现有记录
		err = tx.Exec(`
			UPDATE settlement_records 
			SET base_commission = ?, tax_amount = ?, actual_amount = ?, 
				order_count = ?, upload_batch_id = ?, updated_at = ?
			WHERE id = ?
		`, record.BaseCommission, record.TaxAmount, record.ActualAmount,
			record.OrderCount, batchID, now, existingID).Error
		if err != nil {
			return nil, fmt.Errorf("更新结算记录失败: %v", err)
		}
		settlementID = existingID
	} else {
		// 创建新记录
		err = tx.Exec(`
			INSERT INTO settlement_records 
			(attendant_id, settlement_month, base_commission, tax_amount, actual_amount, 
			 settlement_status, order_count, upload_batch_id, created_at, updated_at)
			VALUES (?, ?, ?, ?, ?, 'calculated', ?, ?, ?, ?)
		`, record.AttendantID, settlementMonth, record.BaseCommission, record.TaxAmount,
			record.ActualAmount, record.OrderCount, batchID, now, now).Error
		if err != nil {
			return nil, fmt.Errorf("创建结算记录失败: %v", err)
		}

		// 获取新创建的记录ID
		err = tx.Raw("SELECT LAST_INSERT_ID()").Scan(&settlementID).Error
		if err != nil {
			return nil, fmt.Errorf("获取结算记录ID失败: %v", err)
		}
	}

	// 记录操作日志
	s.logSettlementOperation(tx, settlementID, "upload", operatorID, operatorName, fmt.Sprintf("批次: %s", batchID))

	return &dto.SettlementUploadResult{
		SettlementID:   settlementID,
		AttendantID:    record.AttendantID,
		AttendantName:  attendantName,
		BaseCommission: record.BaseCommission,
		TaxAmount:      record.TaxAmount,
		ActualAmount:   record.ActualAmount,
		OrderCount:     record.OrderCount,
		Status:         "success",
	}, nil
}

// UploadPaymentRecords 上传打款记录
func (s *SettlementUploadService) UploadPaymentRecords(req *dto.PaymentUploadRequest, operatorID uint, operatorName string) (*dto.PaymentUploadResponse, error) {
	s.logger.Info("开始上传打款记录",
		zap.String("batch_no", req.BatchNo),
		zap.Int("record_count", len(req.Records)),
		zap.String("operator", operatorName))

	response := &dto.PaymentUploadResponse{
		BatchNo:        req.BatchNo,
		PaymentTime:    req.PaymentTime,
		TotalRecords:   len(req.Records),
		SuccessRecords: make([]dto.PaymentUploadResult, 0),
		FailedRecords:  make([]dto.PaymentUploadFailedResult, 0),
		UploadTime:     time.Now(),
	}

	// 开启事务处理
	err := s.db.Transaction(func(tx *gorm.DB) error {
		for _, record := range req.Records {
			result, err := s.processPaymentRecord(tx, &record, req.BatchNo, req.PaymentTime, req.Remark, operatorID, operatorName)
			if err != nil {
				// 记录失败
				failedResult := dto.PaymentUploadFailedResult{
					SettlementID:  record.SettlementID,
					AttendantID:   record.AttendantID,
					AttendantName: record.AttendantName,
					ErrorCode:     "PROCESS_ERROR",
					ErrorMessage:  err.Error(),
				}
				response.FailedRecords = append(response.FailedRecords, failedResult)
				response.FailedCount++
			} else {
				// 记录成功
				response.SuccessRecords = append(response.SuccessRecords, *result)
				response.SuccessCount++
			}
		}
		return nil
	})

	if err != nil {
		s.logger.Error("上传打款记录事务失败", zap.Error(err))
		return nil, fmt.Errorf("上传打款记录失败: %v", err)
	}

	s.logger.Info("打款记录上传完成",
		zap.String("batch_no", req.BatchNo),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failed_count", response.FailedCount))

	return response, nil
}

// processPaymentRecord 处理单条打款记录
func (s *SettlementUploadService) processPaymentRecord(tx *gorm.DB, record *dto.PaymentUploadRecord, batchNo string, paymentTime time.Time, batchRemark string, operatorID uint, operatorName string) (*dto.PaymentUploadResult, error) {
	// 验证结算记录是否存在
	var settlementInfo struct {
		ID              int64   `json:"id"`
		AttendantID     int64   `json:"attendant_id"`
		SettlementMonth string  `json:"settlement_month"`
		ActualAmount    float64 `json:"actual_amount"`
		Status          string  `json:"status"`
	}

	err := tx.Raw(`
		SELECT id, attendant_id, settlement_month, actual_amount, settlement_status as status
		FROM settlement_records 
		WHERE id = ? AND deleted_at IS NULL
	`, record.SettlementID).Scan(&settlementInfo).Error
	if err != nil {
		return nil, fmt.Errorf("结算记录 %d 不存在", record.SettlementID)
	}

	// 验证陪诊师ID是否匹配
	if record.AttendantID > 0 && settlementInfo.AttendantID != record.AttendantID {
		return nil, fmt.Errorf("陪诊师ID不匹配，期望: %d，实际: %d", record.AttendantID, settlementInfo.AttendantID)
	}

	// 验证结算月份是否匹配
	if record.SettlementMonth != "" && settlementInfo.SettlementMonth != record.SettlementMonth {
		return nil, fmt.Errorf("结算月份不匹配，期望: %s，实际: %s", record.SettlementMonth, settlementInfo.SettlementMonth)
	}

	// 检查打款金额是否合理
	if record.PaymentAmount > settlementInfo.ActualAmount {
		return nil, fmt.Errorf("打款金额 %.2f 超过实际应付金额 %.2f", record.PaymentAmount, settlementInfo.ActualAmount)
	}

	// 获取陪诊师姓名
	var attendantName string
	err = tx.Raw(`
		SELECT a.name 
		FROM attendants a 
		WHERE a.user_id = ? AND a.deleted_at IS NULL
	`, settlementInfo.AttendantID).Scan(&attendantName).Error
	if err != nil {
		return nil, fmt.Errorf("获取陪诊师姓名失败: %v", err)
	}

	// 创建打款记录
	now := time.Now()
	var paymentID int64

	err = tx.Exec(`
		INSERT INTO payment_records 
		(settlement_id, batch_no, payment_amount, payment_time, payment_status, remark, created_at, updated_at)
		VALUES (?, ?, ?, ?, 'success', ?, ?, ?)
	`, record.SettlementID, batchNo, record.PaymentAmount, paymentTime,
		record.Remark, now, now).Error
	if err != nil {
		return nil, fmt.Errorf("创建打款记录失败: %v", err)
	}

	// 获取新创建的记录ID
	err = tx.Raw("SELECT LAST_INSERT_ID()").Scan(&paymentID).Error
	if err != nil {
		return nil, fmt.Errorf("获取打款记录ID失败: %v", err)
	}

	// 更新结算记录状态为已打款
	err = tx.Exec(`
		UPDATE settlement_records 
		SET settlement_status = 'paid', updated_at = ?
		WHERE id = ?
	`, now, record.SettlementID).Error
	if err != nil {
		return nil, fmt.Errorf("更新结算记录状态失败: %v", err)
	}

	// 记录操作日志
	s.logPaymentOperation(tx, paymentID, "upload", operatorID, operatorName, fmt.Sprintf("批次: %s", batchNo))

	return &dto.PaymentUploadResult{
		PaymentID:     paymentID,
		SettlementID:  record.SettlementID,
		AttendantID:   settlementInfo.AttendantID,
		AttendantName: attendantName,
		PaymentAmount: record.PaymentAmount,
		PaymentTime:   paymentTime,
		Status:        "success",
	}, nil
}

// ProcessFileUpload 处理文件上传
func (s *SettlementUploadService) ProcessFileUpload(file *multipart.FileHeader, req *dto.FileUploadRequest) (*dto.FileUploadResponse, error) {
	s.logger.Info("开始处理文件上传",
		zap.String("filename", file.Filename),
		zap.String("upload_type", req.UploadType),
		zap.Int64("file_size", file.Size))

	// 打开文件
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %v", err)
	}
	defer src.Close()

	response := &dto.FileUploadResponse{
		UploadType:       req.UploadType,
		FileName:         file.Filename,
		FileSize:         file.Size,
		ValidationErrors: make([]dto.FileValidationError, 0),
		UploadTime:       time.Now(),
	}

	// 根据文件类型处理
	if strings.HasSuffix(strings.ToLower(file.Filename), ".csv") {
		return s.processCSVFile(src, req, response)
	} else {
		return nil, fmt.Errorf("不支持的文件格式，仅支持CSV文件")
	}
}

// processCSVFile 处理CSV文件
func (s *SettlementUploadService) processCSVFile(src io.Reader, req *dto.FileUploadRequest, response *dto.FileUploadResponse) (*dto.FileUploadResponse, error) {
	reader := csv.NewReader(src)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("读取CSV文件失败: %v", err)
	}

	if len(records) == 0 {
		return nil, fmt.Errorf("文件为空")
	}

	response.TotalRows = len(records) - 1 // 减去标题行

	// 根据上传类型处理不同的数据格式
	switch req.UploadType {
	case "settlement":
		return s.processSettlementCSV(records, req, response)
	case "payment":
		return s.processPaymentCSV(records, req, response)
	default:
		return nil, fmt.Errorf("不支持的上传类型: %s", req.UploadType)
	}
}

// processSettlementCSV 处理结算数据CSV
func (s *SettlementUploadService) processSettlementCSV(records [][]string, req *dto.FileUploadRequest, response *dto.FileUploadResponse) (*dto.FileUploadResponse, error) {
	if len(records) < 2 {
		return nil, fmt.Errorf("文件至少需要包含标题行和一行数据")
	}

	// 验证标题行
	expectedHeaders := []string{"陪诊师ID", "陪诊师姓名", "陪诊师手机号", "基础佣金", "纳税额度", "实际到账金额", "订单数量"}
	if len(records[0]) < len(expectedHeaders) {
		return nil, fmt.Errorf("CSV文件列数不足，期望至少 %d 列", len(expectedHeaders))
	}

	var previewData []dto.SettlementUploadRecord
	validRows := 0

	// 处理数据行
	for i, record := range records[1:] {
		rowNum := i + 2 // 从第2行开始

		if len(record) < len(expectedHeaders) {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "全部",
				Value:        strings.Join(record, ","),
				ErrorCode:    "INSUFFICIENT_COLUMNS",
				ErrorMessage: fmt.Sprintf("列数不足，期望 %d 列，实际 %d 列", len(expectedHeaders), len(record)),
			})
			continue
		}

		// 解析数据
		attendantID, err := strconv.ParseInt(strings.TrimSpace(record[0]), 10, 64)
		if err != nil {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "陪诊师ID",
				Value:        record[0],
				ErrorCode:    "INVALID_ATTENDANT_ID",
				ErrorMessage: "陪诊师ID必须为数字",
			})
			continue
		}

		baseCommission, err := strconv.ParseFloat(strings.TrimSpace(record[3]), 64)
		if err != nil {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "基础佣金",
				Value:        record[3],
				ErrorCode:    "INVALID_BASE_COMMISSION",
				ErrorMessage: "基础佣金必须为数字",
			})
			continue
		}

		taxAmount, err := strconv.ParseFloat(strings.TrimSpace(record[4]), 64)
		if err != nil {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "纳税额度",
				Value:        record[4],
				ErrorCode:    "INVALID_TAX_AMOUNT",
				ErrorMessage: "纳税额度必须为数字",
			})
			continue
		}

		actualAmount, err := strconv.ParseFloat(strings.TrimSpace(record[5]), 64)
		if err != nil {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "实际到账金额",
				Value:        record[5],
				ErrorCode:    "INVALID_ACTUAL_AMOUNT",
				ErrorMessage: "实际到账金额必须为数字",
			})
			continue
		}

		orderCount, err := strconv.Atoi(strings.TrimSpace(record[6]))
		if err != nil {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "订单数量",
				Value:        record[6],
				ErrorCode:    "INVALID_ORDER_COUNT",
				ErrorMessage: "订单数量必须为整数",
			})
			continue
		}

		// 创建预览数据
		settlementRecord := dto.SettlementUploadRecord{
			AttendantID:    attendantID,
			AttendantName:  strings.TrimSpace(record[1]),
			AttendantPhone: strings.TrimSpace(record[2]),
			BaseCommission: baseCommission,
			TaxAmount:      taxAmount,
			ActualAmount:   actualAmount,
			OrderCount:     orderCount,
		}

		previewData = append(previewData, settlementRecord)
		validRows++

		// 限制预览数据数量
		if len(previewData) >= 10 {
			break
		}
	}

	response.ValidRows = validRows
	response.InvalidRows = response.TotalRows - validRows
	response.PreviewData = previewData

	return response, nil
}

// processPaymentCSV 处理打款记录CSV
func (s *SettlementUploadService) processPaymentCSV(records [][]string, req *dto.FileUploadRequest, response *dto.FileUploadResponse) (*dto.FileUploadResponse, error) {
	if len(records) < 2 {
		return nil, fmt.Errorf("文件至少需要包含标题行和一行数据")
	}

	// 验证标题行
	expectedHeaders := []string{"结算记录ID", "陪诊师ID", "陪诊师姓名", "结算月份", "打款金额", "备注"}
	if len(records[0]) < len(expectedHeaders) {
		return nil, fmt.Errorf("CSV文件列数不足，期望至少 %d 列", len(expectedHeaders))
	}

	var previewData []dto.PaymentUploadRecord
	validRows := 0

	// 处理数据行
	for i, record := range records[1:] {
		rowNum := i + 2 // 从第2行开始

		if len(record) < len(expectedHeaders) {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "全部",
				Value:        strings.Join(record, ","),
				ErrorCode:    "INSUFFICIENT_COLUMNS",
				ErrorMessage: fmt.Sprintf("列数不足，期望 %d 列，实际 %d 列", len(expectedHeaders), len(record)),
			})
			continue
		}

		// 解析数据
		settlementID, err := strconv.ParseInt(strings.TrimSpace(record[0]), 10, 64)
		if err != nil {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "结算记录ID",
				Value:        record[0],
				ErrorCode:    "INVALID_SETTLEMENT_ID",
				ErrorMessage: "结算记录ID必须为数字",
			})
			continue
		}

		attendantID, err := strconv.ParseInt(strings.TrimSpace(record[1]), 10, 64)
		if err != nil {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "陪诊师ID",
				Value:        record[1],
				ErrorCode:    "INVALID_ATTENDANT_ID",
				ErrorMessage: "陪诊师ID必须为数字",
			})
			continue
		}

		paymentAmount, err := strconv.ParseFloat(strings.TrimSpace(record[4]), 64)
		if err != nil {
			response.ValidationErrors = append(response.ValidationErrors, dto.FileValidationError{
				Row:          rowNum,
				Column:       "打款金额",
				Value:        record[4],
				ErrorCode:    "INVALID_PAYMENT_AMOUNT",
				ErrorMessage: "打款金额必须为数字",
			})
			continue
		}

		// 创建预览数据
		paymentRecord := dto.PaymentUploadRecord{
			SettlementID:    settlementID,
			AttendantID:     attendantID,
			AttendantName:   strings.TrimSpace(record[2]),
			SettlementMonth: strings.TrimSpace(record[3]),
			PaymentAmount:   paymentAmount,
			Remark:          strings.TrimSpace(record[5]),
		}

		previewData = append(previewData, paymentRecord)
		validRows++

		// 限制预览数据数量
		if len(previewData) >= 10 {
			break
		}
	}

	response.ValidRows = validRows
	response.InvalidRows = response.TotalRows - validRows
	response.PreviewData = previewData

	return response, nil
}

// BatchProcess 批量处理确认/回滚
func (s *SettlementUploadService) BatchProcess(req *dto.BatchProcessRequest, operatorID uint, operatorName string) (*dto.BatchProcessResponse, error) {
	s.logger.Info("开始批量处理",
		zap.String("process_type", req.ProcessType),
		zap.String("batch_id", req.BatchID),
		zap.String("operator", operatorName))

	response := &dto.BatchProcessResponse{
		ProcessType:   req.ProcessType,
		BatchID:       req.BatchID,
		ProcessTime:   time.Now(),
		ErrorMessages: make([]string, 0),
	}

	// 开启事务处理
	err := s.db.Transaction(func(tx *gorm.DB) error {
		switch req.ProcessType {
		case "confirm":
			return s.processBatchConfirm(tx, req, response, operatorID, operatorName)
		case "rollback":
			return s.processBatchRollback(tx, req, response, operatorID, operatorName)
		default:
			return fmt.Errorf("不支持的处理类型: %s", req.ProcessType)
		}
	})

	if err != nil {
		s.logger.Error("批量处理失败", zap.Error(err))
		return nil, fmt.Errorf("批量处理失败: %v", err)
	}

	s.logger.Info("批量处理完成",
		zap.String("batch_id", req.BatchID),
		zap.Int("success_count", response.SuccessCount),
		zap.Int("failed_count", response.FailedCount))

	return response, nil
}

// processBatchConfirm 处理批量确认
func (s *SettlementUploadService) processBatchConfirm(tx *gorm.DB, req *dto.BatchProcessRequest, response *dto.BatchProcessResponse, operatorID uint, operatorName string) error {
	// 查询批次相关的记录
	var records []struct {
		ID   int64  `json:"id"`
		Type string `json:"type"`
	}

	// 查询结算记录
	err := tx.Raw(`
		SELECT id, 'settlement' as type 
		FROM settlement_records 
		WHERE upload_batch_id = ? AND deleted_at IS NULL
	`, req.BatchID).Scan(&records).Error
	if err != nil {
		return fmt.Errorf("查询结算记录失败: %v", err)
	}

	// 查询打款记录
	var paymentRecords []struct {
		ID   int64  `json:"id"`
		Type string `json:"type"`
	}
	err = tx.Raw(`
		SELECT id, 'payment' as type 
		FROM payment_records 
		WHERE batch_no = ? AND deleted_at IS NULL
	`, req.BatchID).Scan(&paymentRecords).Error
	if err != nil {
		return fmt.Errorf("查询打款记录失败: %v", err)
	}

	records = append(records, paymentRecords...)
	response.ProcessedCount = len(records)

	// 确认操作：更新记录状态或添加确认标记
	for _, record := range records {
		if record.Type == "settlement" {
			// 结算记录确认逻辑
			err = tx.Exec(`
				UPDATE settlement_records 
				SET settlement_status = 'calculated', updated_at = ?
				WHERE id = ?
			`, time.Now(), record.ID).Error
			if err != nil {
				response.ErrorMessages = append(response.ErrorMessages, fmt.Sprintf("确认结算记录 %d 失败: %v", record.ID, err))
				response.FailedCount++
			} else {
				response.SuccessCount++
			}
		} else if record.Type == "payment" {
			// 打款记录确认逻辑
			err = tx.Exec(`
				UPDATE payment_records 
				SET payment_status = 'success', updated_at = ?
				WHERE id = ?
			`, time.Now(), record.ID).Error
			if err != nil {
				response.ErrorMessages = append(response.ErrorMessages, fmt.Sprintf("确认打款记录 %d 失败: %v", record.ID, err))
				response.FailedCount++
			} else {
				response.SuccessCount++
			}
		}
	}

	return nil
}

// processBatchRollback 处理批量回滚
func (s *SettlementUploadService) processBatchRollback(tx *gorm.DB, req *dto.BatchProcessRequest, response *dto.BatchProcessResponse, operatorID uint, operatorName string) error {
	// 软删除批次相关的记录
	now := time.Now()

	// 回滚结算记录
	var settlementCount int64
	err := tx.Exec(`
		UPDATE settlement_records 
		SET deleted_at = ?, updated_at = ?
		WHERE upload_batch_id = ? AND deleted_at IS NULL
	`, now, now, req.BatchID).Error
	if err != nil {
		return fmt.Errorf("回滚结算记录失败: %v", err)
	}

	err = tx.Raw(`
		SELECT COUNT(*) FROM settlement_records 
		WHERE upload_batch_id = ? AND deleted_at = ?
	`, req.BatchID, now).Scan(&settlementCount).Error
	if err != nil {
		return fmt.Errorf("统计回滚结算记录失败: %v", err)
	}

	// 回滚打款记录
	var paymentCount int64
	err = tx.Exec(`
		UPDATE payment_records 
		SET deleted_at = ?, updated_at = ?
		WHERE batch_no = ? AND deleted_at IS NULL
	`, now, now, req.BatchID).Error
	if err != nil {
		return fmt.Errorf("回滚打款记录失败: %v", err)
	}

	err = tx.Raw(`
		SELECT COUNT(*) FROM payment_records 
		WHERE batch_no = ? AND deleted_at = ?
	`, req.BatchID, now).Scan(&paymentCount).Error
	if err != nil {
		return fmt.Errorf("统计回滚打款记录失败: %v", err)
	}

	response.ProcessedCount = int(settlementCount + paymentCount)
	response.SuccessCount = response.ProcessedCount

	return nil
}

// ValidateAttendantInfo 验证陪诊师信息
func (s *SettlementUploadService) ValidateAttendantInfo(attendantID int64, name, phone string) error {
	return s.validateAttendantInfoInTx(s.db, attendantID, name, phone)
}

// validateAttendantInfoInTx 在事务中验证陪诊师信息
func (s *SettlementUploadService) validateAttendantInfoInTx(tx *gorm.DB, attendantID int64, name, phone string) error {
	var attendant struct {
		ID    int64  `json:"id"`
		Name  string `json:"name"`
		Phone string `json:"phone"`
	}

	err := tx.Raw(`
		SELECT a.user_id as id, a.name, u.phone 
		FROM attendants a 
		LEFT JOIN users u ON a.user_id = u.id 
		WHERE a.user_id = ? AND a.deleted_at IS NULL
	`, attendantID).Scan(&attendant).Error

	if err != nil {
		return fmt.Errorf("陪诊师 %d 不存在", attendantID)
	}

	// 验证姓名（如果提供）
	if name != "" && attendant.Name != name {
		return fmt.Errorf("陪诊师姓名不匹配，期望: %s，实际: %s", name, attendant.Name)
	}

	// 验证手机号（如果提供）
	if phone != "" && attendant.Phone != phone {
		return fmt.Errorf("陪诊师手机号不匹配，期望: %s，实际: %s", phone, attendant.Phone)
	}

	return nil
}

// CheckSettlementExists 检查结算记录是否存在
func (s *SettlementUploadService) CheckSettlementExists(attendantID int64, settlementMonth string) (bool, error) {
	exists, _, err := s.checkSettlementExistsInTx(s.db, attendantID, settlementMonth)
	return exists, err
}

// checkSettlementExistsInTx 在事务中检查结算记录是否存在
func (s *SettlementUploadService) checkSettlementExistsInTx(tx *gorm.DB, attendantID int64, settlementMonth string) (bool, int64, error) {
	var count int64
	var id int64

	err := tx.Raw(`
		SELECT COUNT(*), COALESCE(MAX(id), 0) 
		FROM settlement_records 
		WHERE attendant_id = ? AND settlement_month = ? AND deleted_at IS NULL
	`, attendantID, settlementMonth).Row().Scan(&count, &id)

	if err != nil {
		return false, 0, err
	}

	return count > 0, id, nil
}

// logSettlementOperation 记录结算操作日志
func (s *SettlementUploadService) logSettlementOperation(tx *gorm.DB, settlementID int64, operation string, operatorID uint, operatorName, remark string) {
	err := tx.Exec(`
		INSERT INTO operation_logs 
		(operation_type, target_type, target_id, operator_id, operator_name, remark, created_at)
		VALUES (?, 'settlement', ?, ?, ?, ?, ?)
	`, operation, settlementID, operatorID, operatorName, remark, time.Now()).Error

	if err != nil {
		s.logger.Warn("记录结算操作日志失败", zap.Error(err), zap.Int64("settlement_id", settlementID))
	}
}

// logPaymentOperation 记录打款操作日志
func (s *SettlementUploadService) logPaymentOperation(tx *gorm.DB, paymentID int64, operation string, operatorID uint, operatorName, remark string) {
	err := tx.Exec(`
		INSERT INTO operation_logs 
		(operation_type, target_type, target_id, operator_id, operator_name, remark, created_at)
		VALUES (?, 'payment', ?, ?, ?, ?, ?)
	`, operation, paymentID, operatorID, operatorName, remark, time.Now()).Error

	if err != nil {
		s.logger.Warn("记录打款操作日志失败", zap.Error(err), zap.Int64("payment_id", paymentID))
	}
}
