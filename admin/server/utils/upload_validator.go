package utils

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// UploadValidator 上传数据验证器
type UploadValidator struct{}

// NewUploadValidator 创建上传数据验证器
func NewUploadValidator() *UploadValidator {
	return &UploadValidator{}
}

// ValidateSettlementMonth 验证结算月份格式
func (v *UploadValidator) ValidateSettlementMonth(month string) error {
	if month == "" {
		return fmt.Errorf("结算月份不能为空")
	}

	// 验证格式 YYYY-MM
	matched, err := regexp.MatchString(`^\d{4}-\d{2}$`, month)
	if err != nil {
		return fmt.Errorf("结算月份格式验证失败: %v", err)
	}
	if !matched {
		return fmt.Errorf("结算月份格式错误，应为 YYYY-MM 格式")
	}

	// 验证是否为有效日期
	_, err = time.Parse("2006-01", month)
	if err != nil {
		return fmt.Errorf("结算月份日期无效: %v", err)
	}

	// 验证月份不能是未来月份
	now := time.Now()
	currentMonth := now.Format("2006-01")
	if month > currentMonth {
		return fmt.Errorf("结算月份不能是未来月份")
	}

	return nil
}

// ValidateAttendantID 验证陪诊师ID
func (v *UploadValidator) ValidateAttendantID(idStr string) (int64, error) {
	if idStr == "" {
		return 0, fmt.Errorf("陪诊师ID不能为空")
	}

	id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
	if err != nil {
		return 0, fmt.Errorf("陪诊师ID必须为数字")
	}

	if id <= 0 {
		return 0, fmt.Errorf("陪诊师ID必须大于0")
	}

	return id, nil
}

// ValidateAmount 验证金额
func (v *UploadValidator) ValidateAmount(amountStr string, fieldName string) (float64, error) {
	if amountStr == "" {
		return 0, fmt.Errorf("%s不能为空", fieldName)
	}

	amount, err := strconv.ParseFloat(strings.TrimSpace(amountStr), 64)
	if err != nil {
		return 0, fmt.Errorf("%s必须为数字", fieldName)
	}

	if amount < 0 {
		return 0, fmt.Errorf("%s不能为负数", fieldName)
	}

	// 验证小数位数不超过2位
	amountStr = strings.TrimSpace(amountStr)
	if strings.Contains(amountStr, ".") {
		parts := strings.Split(amountStr, ".")
		if len(parts) == 2 && len(parts[1]) > 2 {
			return 0, fmt.Errorf("%s小数位数不能超过2位", fieldName)
		}
	}

	return amount, nil
}

// ValidateOrderCount 验证订单数量
func (v *UploadValidator) ValidateOrderCount(countStr string) (int, error) {
	if countStr == "" {
		return 0, nil // 订单数量可以为空，默认为0
	}

	count, err := strconv.Atoi(strings.TrimSpace(countStr))
	if err != nil {
		return 0, fmt.Errorf("订单数量必须为整数")
	}

	if count < 0 {
		return 0, fmt.Errorf("订单数量不能为负数")
	}

	return count, nil
}

// ValidatePhoneNumber 验证手机号格式
func (v *UploadValidator) ValidatePhoneNumber(phone string) error {
	if phone == "" {
		return nil // 手机号可以为空
	}

	phone = strings.TrimSpace(phone)

	// 验证中国大陆手机号格式
	matched, err := regexp.MatchString(`^1[3-9]\d{9}$`, phone)
	if err != nil {
		return fmt.Errorf("手机号格式验证失败: %v", err)
	}
	if !matched {
		return fmt.Errorf("手机号格式错误")
	}

	return nil
}

// ValidateName 验证姓名
func (v *UploadValidator) ValidateName(name string) error {
	if name == "" {
		return nil // 姓名可以为空
	}

	name = strings.TrimSpace(name)

	if len(name) < 2 || len(name) > 20 {
		return fmt.Errorf("姓名长度应在2-20个字符之间")
	}

	// 验证姓名只包含中文、英文字母和空格
	matched, err := regexp.MatchString(`^[\u4e00-\u9fa5a-zA-Z\s]+$`, name)
	if err != nil {
		return fmt.Errorf("姓名格式验证失败: %v", err)
	}
	if !matched {
		return fmt.Errorf("姓名只能包含中文、英文字母和空格")
	}

	return nil
}

// ValidateBatchNo 验证批次号
func (v *UploadValidator) ValidateBatchNo(batchNo string) error {
	if batchNo == "" {
		return fmt.Errorf("批次号不能为空")
	}

	batchNo = strings.TrimSpace(batchNo)

	if len(batchNo) < 3 || len(batchNo) > 50 {
		return fmt.Errorf("批次号长度应在3-50个字符之间")
	}

	// 验证批次号只包含字母、数字、下划线和连字符
	matched, err := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, batchNo)
	if err != nil {
		return fmt.Errorf("批次号格式验证失败: %v", err)
	}
	if !matched {
		return fmt.Errorf("批次号只能包含字母、数字、下划线和连字符")
	}

	return nil
}

// ValidatePaymentTime 验证打款时间
func (v *UploadValidator) ValidatePaymentTime(timeStr string) (time.Time, error) {
	if timeStr == "" {
		return time.Time{}, fmt.Errorf("打款时间不能为空")
	}

	// 支持多种时间格式
	formats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05",
		"2006-01-02 15:04",
		"2006-01-02",
	}

	var paymentTime time.Time
	var err error

	for _, format := range formats {
		paymentTime, err = time.Parse(format, strings.TrimSpace(timeStr))
		if err == nil {
			break
		}
	}

	if err != nil {
		return time.Time{}, fmt.Errorf("打款时间格式错误，支持格式: YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD")
	}

	// 验证打款时间不能是未来时间
	if paymentTime.After(time.Now()) {
		return time.Time{}, fmt.Errorf("打款时间不能是未来时间")
	}

	return paymentTime, nil
}

// ValidateCSVHeaders 验证CSV文件标题行
func (v *UploadValidator) ValidateCSVHeaders(headers []string, expectedHeaders []string) error {
	if len(headers) < len(expectedHeaders) {
		return fmt.Errorf("CSV文件列数不足，期望至少 %d 列，实际 %d 列", len(expectedHeaders), len(headers))
	}

	// 检查必要的列是否存在（不区分大小写，允许前后空格）
	headerMap := make(map[string]int)
	for i, header := range headers {
		headerMap[strings.TrimSpace(strings.ToLower(header))] = i
	}

	for i, expectedHeader := range expectedHeaders {
		expectedKey := strings.TrimSpace(strings.ToLower(expectedHeader))
		if _, exists := headerMap[expectedKey]; !exists {
			return fmt.Errorf("缺少必要的列: %s (第%d列)", expectedHeader, i+1)
		}
	}

	return nil
}

// ValidateFileSize 验证文件大小
func (v *UploadValidator) ValidateFileSize(size int64, maxSize int64) error {
	if size <= 0 {
		return fmt.Errorf("文件大小无效")
	}

	if size > maxSize {
		return fmt.Errorf("文件大小超过限制，最大允许 %d MB", maxSize/(1024*1024))
	}

	return nil
}

// ValidateFileExtension 验证文件扩展名
func (v *UploadValidator) ValidateFileExtension(filename string, allowedExtensions []string) error {
	if filename == "" {
		return fmt.Errorf("文件名不能为空")
	}

	filename = strings.ToLower(filename)

	for _, ext := range allowedExtensions {
		if strings.HasSuffix(filename, strings.ToLower(ext)) {
			return nil
		}
	}

	return fmt.Errorf("不支持的文件格式，仅支持: %s", strings.Join(allowedExtensions, ", "))
}
