# 结算数据上传API文档

## 概述

结算数据上传功能为管理后台提供了批量上传结算明细数据和打款记录的能力，支持JSON数据上传和CSV文件上传两种方式。

## API端点

### 1. 上传结算数据

**POST** `/api/admin/settlement/upload/data`

上传结算明细数据，支持批量创建或更新结算记录。

#### 请求参数

```json
{
  "settlement_month": "2024-01",
  "records": [
    {
      "attendant_id": 1,
      "attendant_name": "张三",
      "attendant_phone": "13800138000",
      "base_commission": 1000.00,
      "tax_amount": 60.00,
      "actual_amount": 940.00,
      "order_count": 10
    }
  ],
  "batch_remark": "2024年1月结算数据",
  "overwrite_mode": false
}
```

#### 参数说明

- `settlement_month`: 结算月份，格式为 YYYY-MM
- `records`: 结算记录数组，最多1000条
- `batch_remark`: 批次备注（可选）
- `overwrite_mode`: 是否覆盖已存在记录，默认false

#### 响应示例

```json
{
  "code": 0,
  "message": "结算数据上传完成",
  "data": {
    "batch_id": "SETTLEMENT_2024-01_12345678",
    "settlement_month": "2024-01",
    "total_records": 1,
    "success_count": 1,
    "failed_count": 0,
    "success_records": [
      {
        "settlement_id": 1,
        "attendant_id": 1,
        "attendant_name": "张三",
        "base_commission": 1000.00,
        "tax_amount": 60.00,
        "actual_amount": 940.00,
        "order_count": 10,
        "status": "success"
      }
    ],
    "failed_records": [],
    "upload_time": "2024-01-15T10:30:00Z"
  }
}
```

### 2. 上传打款记录

**POST** `/api/admin/settlement/upload/payment`

上传打款记录，更新结算记录状态为已打款。

#### 请求参数

```json
{
  "batch_no": "PAY_2024_001",
  "payment_time": "2024-01-15T10:00:00Z",
  "records": [
    {
      "settlement_id": 1,
      "payment_amount": 940.00,
      "attendant_id": 1,
      "attendant_name": "张三",
      "settlement_month": "2024-01",
      "remark": "正常打款"
    }
  ],
  "remark": "2024年1月打款批次"
}
```

#### 参数说明

- `batch_no`: 打款批次号，必须唯一
- `payment_time`: 打款时间
- `records`: 打款记录数组，最多1000条
- `remark`: 批次备注（可选）

#### 响应示例

```json
{
  "code": 0,
  "message": "打款记录上传完成",
  "data": {
    "batch_no": "PAY_2024_001",
    "payment_time": "2024-01-15T10:00:00Z",
    "total_records": 1,
    "success_count": 1,
    "failed_count": 0,
    "success_records": [
      {
        "payment_id": 1,
        "settlement_id": 1,
        "attendant_id": 1,
        "attendant_name": "张三",
        "payment_amount": 940.00,
        "payment_time": "2024-01-15T10:00:00Z",
        "status": "success"
      }
    ],
    "failed_records": [],
    "upload_time": "2024-01-15T10:30:00Z"
  }
}
```

### 3. 文件上传

**POST** `/api/admin/settlement/upload/file`

支持CSV文件上传，自动解析并验证数据。

#### 请求参数（multipart/form-data）

- `file`: 上传的CSV文件
- `upload_type`: 上传类型，`settlement` 或 `payment`
- `settlement_month`: 结算月份（结算数据上传时必填）
- `batch_no`: 批次号（打款记录上传时必填）
- `payment_time`: 打款时间（打款记录上传时必填）
- `overwrite_mode`: 是否覆盖已存在记录
- `remark`: 备注

#### CSV文件格式

**结算数据CSV格式：**
```csv
陪诊师ID,陪诊师姓名,陪诊师手机号,基础佣金,纳税额度,实际到账金额,订单数量
1,张三,13800138000,1000.00,60.00,940.00,10
2,李四,13800138001,800.00,48.00,752.00,8
```

**打款记录CSV格式：**
```csv
结算记录ID,陪诊师ID,陪诊师姓名,结算月份,打款金额,备注
1,1,张三,2024-01,940.00,正常打款
2,2,李四,2024-01,752.00,正常打款
```

#### 响应示例

```json
{
  "code": 0,
  "message": "文件上传处理完成",
  "data": {
    "upload_type": "settlement",
    "file_name": "settlement_2024_01.csv",
    "file_size": 1024,
    "total_rows": 2,
    "valid_rows": 2,
    "invalid_rows": 0,
    "preview_data": [
      {
        "attendant_id": 1,
        "attendant_name": "张三",
        "attendant_phone": "13800138000",
        "base_commission": 1000.00,
        "tax_amount": 60.00,
        "actual_amount": 940.00,
        "order_count": 10
      }
    ],
    "validation_errors": [],
    "upload_time": "2024-01-15T10:30:00Z"
  }
}
```

### 4. 批量处理（确认/回滚）

**POST** `/api/admin/settlement/upload/batch-process`

对上传的批次数据进行确认或回滚操作。

#### 请求参数

```json
{
  "process_type": "confirm",
  "batch_id": "SETTLEMENT_2024-01_12345678",
  "record_ids": [1, 2, 3],
  "reason": "数据确认无误"
}
```

#### 参数说明

- `process_type`: 处理类型，`confirm`（确认）或 `rollback`（回滚）
- `batch_id`: 批次ID
- `record_ids`: 指定记录ID（可选，为空时处理整个批次）
- `reason`: 处理原因

### 5. 验证陪诊师信息

**GET** `/api/admin/settlement/upload/validate-attendant`

验证陪诊师信息是否正确。

#### 请求参数

- `attendant_id`: 陪诊师ID（必填）
- `name`: 陪诊师姓名（可选）
- `phone`: 陪诊师手机号（可选）

#### 响应示例

```json
{
  "code": 0,
  "message": "陪诊师信息验证通过",
  "data": {
    "attendant_id": 1,
    "name": "张三",
    "phone": "13800138000",
    "valid": true
  }
}
```

### 6. 检查结算记录是否存在

**GET** `/api/admin/settlement/upload/check-exists`

检查指定陪诊师和月份的结算记录是否已存在。

#### 请求参数

- `attendant_id`: 陪诊师ID（必填）
- `settlement_month`: 结算月份（必填）

#### 响应示例

```json
{
  "code": 0,
  "message": "检查完成",
  "data": {
    "attendant_id": 1,
    "settlement_month": "2024-01",
    "exists": true
  }
}
```

### 7. 获取上传历史记录

**GET** `/api/admin/settlement/upload/history`

获取上传历史记录列表。

#### 请求参数

- `upload_type`: 上传类型（可选）
- `start_date`: 开始日期（可选）
- `end_date`: 结束日期（可选）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "详细错误信息"
}
```

### 数据验证错误

当上传的数据存在验证错误时，会在响应中返回详细的错误信息：

```json
{
  "code": 0,
  "message": "结算数据上传完成",
  "data": {
    "batch_id": "SETTLEMENT_2024-01_12345678",
    "total_records": 2,
    "success_count": 1,
    "failed_count": 1,
    "success_records": [...],
    "failed_records": [
      {
        "attendant_id": 999,
        "attendant_name": "不存在的陪诊师",
        "error_code": "PROCESS_ERROR",
        "error_message": "陪诊师信息验证失败: 陪诊师 999 不存在",
        "field_errors": {
          "attendant_id": "陪诊师不存在"
        }
      }
    ]
  }
}
```

## 使用流程

### 结算数据上传流程

1. **准备数据**：准备结算明细数据，可以是JSON格式或CSV文件
2. **验证陪诊师信息**：使用验证接口确认陪诊师信息正确
3. **检查重复记录**：使用检查接口确认是否存在重复记录
4. **上传数据**：调用上传接口提交数据
5. **处理结果**：根据响应结果处理成功和失败的记录
6. **确认或回滚**：使用批量处理接口确认数据或回滚操作

### 打款记录上传流程

1. **确认结算记录**：确保相关的结算记录已存在且状态为"已结算"
2. **准备打款数据**：准备打款记录数据
3. **上传打款记录**：调用打款记录上传接口
4. **验证结果**：确认打款记录创建成功，结算记录状态更新为"已打款"

## 注意事项

1. **数据量限制**：单次上传最多支持1000条记录
2. **文件大小限制**：上传文件大小不能超过10MB
3. **数据格式**：金额字段最多支持2位小数
4. **唯一性约束**：同一陪诊师在同一月份只能有一条结算记录
5. **状态流转**：结算记录状态流转为：pending -> calculated -> paid
6. **权限要求**：所有接口都需要管理员权限
7. **操作日志**：所有上传操作都会记录操作日志

## 示例代码

### JavaScript示例

```javascript
// 上传结算数据
async function uploadSettlementData(data) {
  const response = await fetch('/api/admin/settlement/upload/data', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify(data)
  });
  
  return await response.json();
}

// 文件上传
async function uploadFile(file, uploadType, settlementMonth) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('upload_type', uploadType);
  formData.append('settlement_month', settlementMonth);
  
  const response = await fetch('/api/admin/settlement/upload/file', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + token
    },
    body: formData
  });
  
  return await response.json();
}
```

### cURL示例

```bash
# 上传结算数据
curl -X POST "http://localhost:8082/api/admin/settlement/upload/data" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "settlement_month": "2024-01",
    "records": [
      {
        "attendant_id": 1,
        "attendant_name": "张三",
        "attendant_phone": "13800138000",
        "base_commission": 1000.00,
        "tax_amount": 60.00,
        "actual_amount": 940.00,
        "order_count": 10
      }
    ]
  }'

# 文件上传
curl -X POST "http://localhost:8082/api/admin/settlement/upload/file" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@settlement_data.csv" \
  -F "upload_type=settlement" \
  -F "settlement_month=2024-01"
```