package router

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/gemeijie/peizhen/admin/server/client"
	"github.com/gemeijie/peizhen/admin/server/config"
	"github.com/gemeijie/peizhen/admin/server/controller"
	"github.com/gemeijie/peizhen/admin/server/handler"
	"github.com/gemeijie/peizhen/admin/server/middleware"
	"github.com/gemeijie/peizhen/admin/server/pkg/wechat"
	"github.com/gemeijie/peizhen/admin/server/repository"
	repositoryImpl "github.com/gemeijie/peizhen/admin/server/repository/impl"
	"github.com/gemeijie/peizhen/admin/server/scheduler"
	"github.com/gemeijie/peizhen/admin/server/service"
	"github.com/gemeijie/peizhen/admin/server/service/impl"
	serviceImpl "github.com/gemeijie/peizhen/admin/server/service/impl"
	"github.com/gemeijie/peizhen/admin/server/utils/logger"
)

// SetupRoutes 设置所有路由
func SetupRoutes(r *gin.Engine, db *gorm.DB, refundHandler *handler.RefundHandler,
	wechatRefundSvc service.IWechatRefundService, refundRepo repository.IRefundRepository, cfg *config.Config) {
	// 健康检查路由
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"time":    time.Now().Format("2006-01-02 15:04:05"),
			"service": "admin-server",
			"features": []string{
				"refund-management",
				"monitoring",
				"service-management",
			},
		})
	})

	// 初始化Repository层
	adminRepo := repository.NewAdminRepository(db)
	attendantRepo := repository.NewAttendantRepository(db)
	userRepo := repository.NewUserRepository(db)
	orderRepo := repositoryImpl.NewOrderRepository(db)
	withdrawalRepo := repositoryImpl.NewWithdrawalRepository(db)
	approvalRepo := repositoryImpl.NewApprovalRepository(db)
	accountRepo := repository.NewAccountRepository(db)

	// 获取logger实例（需要在后端服务初始化之前）
	appLogger := logger.GetLogger()

	// 初始化微信客户端
	wechatClient, err := wechat.NewClient(cfg.Wechat)
	if err != nil {
		panic("Failed to initialize wechat client: " + err.Error())
	}

	// 初始化Service层
	adminService := service.NewAdminService(adminRepo)
	authService := service.NewAuthService(adminRepo)
	messageService := service.NewMessageService(db)
	userService := service.NewUserService(userRepo)
	orderService := serviceImpl.NewOrderService(orderRepo)
	orderMatchingService := serviceImpl.NewOrderMatchingService(db)

	// 创建增强的人工分配服务
	enhancedManualAssignmentService := service.NewEnhancedManualAssignmentService(db, orderMatchingService)

	// 使用传入的配置参数
	orderStatusService := serviceImpl.NewOrderStatusService(db, orderRepo, refundRepo, cfg) // 添加配置参数

	// 初始化订单完成服务（管理员版本）
	orderCompletionService := serviceImpl.NewOrderCompletionService(db, orderRepo)

	// 创建RefundService（需要在ApprovalService之前创建）
	refundService := serviceImpl.NewRefundService(refundRepo, orderRepo, orderStatusService, wechatRefundSvc, cfg)

	// 创建转账服务
	transferService := serviceImpl.NewTransferService(wechatClient, cfg)
	withdrawalService := serviceImpl.NewWithdrawalService(withdrawalRepo, transferService, accountRepo)

	// 创建账户服务
	accountService := serviceImpl.NewAccountService(accountRepo, userRepo)
	// 创建审批服务
	approvalService := serviceImpl.NewRefundApprovalService(approvalRepo, refundRepo, orderStatusService, refundService)
	retryService := serviceImpl.NewRefundRetryService(refundRepo, db)
	monitoringService := serviceImpl.NewRefundMonitoringService(refundRepo, approvalRepo)

	// 创建RefundService用于ApprovalHandler - 已经在上面创建了，这里删除重复创建
	// refundService := serviceImpl.NewRefundService(refundRepo, orderRepo, wechatRefundSvc, cfg)

	// logger实例已在上面初始化

	// 初始化重试调度器
	retryConfig := &config.RetryConfig{
		MaxRetryCount: 3,
		RetryInterval: 30 * time.Second,
		BackoffFactor: 2.0,
		MaxRetryDelay: 10 * time.Minute,
		CheckInterval: 1 * time.Minute,
		Enabled:       true,
	}
	retryScheduler := scheduler.NewRefundRetryScheduler(retryService, retryConfig)

	// 管理系统不应该处理订单超时，这应该由陪诊小程序后端处理
	// 移除订单超时处理调度器

	// 导入logrus库
	// 创建logrus logger实例
	logrusLogger := logrus.New()

	// 初始化处理器
	monitoringHandler := handler.NewMonitoringHandler(monitoringService, logrusLogger)
	serviceHandler := handler.NewServiceHandler(db)
	authHandler := handler.NewAuthHandler(authService)
	attendantHandler := handler.NewAttendantHandler(attendantRepo, messageService)
	userHandler := handler.NewUserHandler(userService)
	orderHandler := handler.NewOrderHandler(orderService, orderMatchingService, enhancedManualAssignmentService)
	orderMatchingHandler := handler.NewOrderMatchingHandler(orderMatchingService)
	orderStatusHandler := handler.NewOrderStatusHandler(orderStatusService)                 // 添加订单状态处理器
	orderCompletionHandler := handler.NewOrderCompletionHandler(db, orderCompletionService) // 添加订单完成处理器
	commissionHandler := handler.NewCommissionHandler(db, appLogger)
	settlementHandler := handler.NewSettlementHandler(db, appLogger)
	financialHandler := handler.NewFinancialHandler(db, appLogger)

	// 初始化Backend API客户端
	backendAPIConfig := client.BackendAPIConfig{
		BaseURL: cfg.Backend.API.BaseURL,
		Timeout: cfg.Backend.API.Timeout,
		APIKey: client.APIKeyConfig{
			KeyID:     cfg.Backend.API.APIKey.KeyID,
			SecretKey: cfg.Backend.API.APIKey.SecretKey,
			Algorithm: cfg.Backend.API.APIKey.Algorithm,
			TTL:       cfg.Backend.API.APIKey.TTL,
		},
	}
	backendClient := client.NewBackendAPIClient(backendAPIConfig, appLogger.Sugar())

	withdrawalHandler := handler.NewWithdrawalHandler(withdrawalService, backendClient, appLogger.Sugar())
	approvalHandler := handler.NewApprovalHandler(approvalService, refundService, db)
	accountController := controller.NewAccountController(accountService)

	// 标准API路由组 (/api/admin/*)
	api := r.Group("/api")
	{
		admin := api.Group("/admin")
		{
			// 认证相关路由（登录、注销等，不需要认证中间件）
			SetupAuthRoutes(admin, authHandler)

			// 以下路由需要认证
			authorizedGroup := admin.Group("")
			authorizedGroup.Use(middleware.JWTAuthMiddleware(authService))
			authorizedGroup.Use(middleware.Logger())

			// 订单审核管理API
			reviewHandler := handler.NewReviewHandler(db, appLogger, orderRepo, cfg)
			SetupReviewRoutes(authorizedGroup, reviewHandler)

			// 监控API
			SetupMonitoringRoutes(authorizedGroup, monitoringHandler)

			// 退款管理API (统一标准路径)
			SetupRefundRoutes(authorizedGroup, refundHandler, db)

			// 服务管理API
			SetupServiceRoutes(authorizedGroup, serviceHandler)

			// 陪诊师管理API
			SetupAttendantRoutes(authorizedGroup, attendantHandler)

			// 用户管理API
			SetupUserRoutes(authorizedGroup, userHandler)

			// 订单管理API
			SetupOrderRoutes(authorizedGroup, orderHandler)

			// 订单匹配管理API
			RegisterOrderMatchingRoutes(authorizedGroup, orderMatchingHandler)

			// 订单状态管理API
			SetupOrderStatusRoutes(authorizedGroup, orderStatusHandler)

			// 订单完成管理API
			SetupOrderCompletionRoutes(authorizedGroup, orderCompletionHandler)

			// 费率管理API
			SetupCommissionRoutes(authorizedGroup, commissionHandler)

			// 分账管理API
			settlementUploadService := impl.NewSettlementUploadService(db, appLogger)
			settlementUploadHandler := handler.NewSettlementUploadHandler(settlementUploadService, appLogger)
			SetupSettlementRoutes(authorizedGroup, settlementHandler, settlementUploadHandler)

			// 财务管理API
			SetupFinancialRoutes(authorizedGroup, financialHandler)

			// 提现管理API
			SetupWithdrawalRoutes(authorizedGroup, withdrawalHandler)

			// 审批管理API (注意：审批路由需要使用整个gin.Engine)
			// SetupApprovalRoutes 将在主路由设置后调用

			// 管理员管理API
			adminHandler := handler.NewAdminHandler(adminService, authService)
			SetupAdminRoutes(authorizedGroup, adminHandler)

			// 系统配置管理API
			SetupSystemConfigRoutes(authorizedGroup, appLogger, db)

			// 操作日志管理API
			operationLogRepo := repositoryImpl.NewOperationLogRepository(db)
			operationLogService := impl.NewOperationLogService(operationLogRepo, appLogger)
			operationLogHandler := handler.NewOperationLogHandler(operationLogService, appLogger)
			SetupOperationLogRoutes(authorizedGroup, operationLogHandler)

			// 账户管理API
			SetupAccountRoutes(authorizedGroup, accountController)
		}
	}

	// 添加微信通知回调路由
	wechatNotifyHandler := handler.NewWechatNotifyHandler(wechatRefundSvc, refundRepo, appLogger)
	wechatNotifyGroup := r.Group("/api/wechat/notify")
	{
		wechatNotifyGroup.POST("/refund", wechatNotifyHandler.HandleRefundNotify)
	}

	// 设置审批路由（需要传入整个gin.Engine）
	SetupApprovalRoutes(r, approvalHandler)

	// 启动重试调度器
	if err := retryScheduler.Start(); err != nil {
		appLogger.Error("启动重试调度器失败", zap.Error(err))
	} else {
		appLogger.Info("重试调度器启动成功")
	}

	// 调试路由 - 生产环境需要管理员权限或IP白名单
	debugGroup := r.Group("/debug")

	// 检查是否为生产环境
	if gin.Mode() == gin.ReleaseMode {
		// 生产环境：需要管理员认证 + IP白名单
		debugGroup.Use(middleware.JWTAuthMiddleware(authService))
		debugGroup.Use(middleware.RoleMiddleware("admin")) // 需要管理员角色
		// 可选：添加IP白名单限制
		// allowedIPs := []string{"127.0.0.1", "内网IP"}
		// debugGroup.Use(middleware.IPWhitelistMiddleware(allowedIPs))
	}

	debugGroup.GET("/routes", func(c *gin.Context) {
		routes := []gin.H{}
		for _, route := range r.Routes() {
			routes = append(routes, gin.H{
				"method": route.Method,
				"path":   route.Path,
			})
		}
		c.JSON(200, gin.H{
			"routes": routes,
			"refund_system": gin.H{
				"status":     "integrated",
				"version":    "v1.0.1",
				"api_prefix": "/api/admin",
				"features": []string{
					"create_refund",
					"process_refund",
					"refund_list",
					"refund_stats",
					"batch_process",
				},
			},
		})
	})
}
