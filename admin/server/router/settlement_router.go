package router

import (
	"github.com/gemeijie/peizhen/admin/server/handler"
	"github.com/gin-gonic/gin"
)

// SetupSettlementRoutes 设置分账管理路由
func SetupSettlementRoutes(r *gin.RouterGroup, settlementHandler *handler.SettlementHandler, uploadHandler *handler.SettlementUploadHandler) {
	settlement := r.Group("/settlement")
	{
		settlement.GET("/list", settlementHandler.GetSettlementList)             // 获取分账记录列表
		settlement.POST("/batch", settlementHandler.TriggerBatchSettlement)      // 触发批量结算
		settlement.POST("/recalculate", settlementHandler.RecalculateSettlement) // 重新计算分账
		settlement.GET("/:id", settlementHandler.GetSettlementDetail)            // 获取分账详情

		// 数据上传相关路由
		upload := settlement.Group("/upload")
		{
			upload.POST("/data", uploadHandler.UploadSettlementData)           // 上传结算数据
			upload.POST("/payment", uploadHandler.UploadPaymentRecords)        // 上传打款记录
			upload.POST("/file", uploadHandler.UploadFile)                     // 文件上传处理
			upload.POST("/batch-process", uploadHandler.BatchProcess)          // 批量处理（确认/回滚）
			upload.GET("/validate-attendant", uploadHandler.ValidateAttendant) // 验证陪诊师信息
			upload.GET("/check-exists", uploadHandler.CheckSettlementExists)   // 检查结算记录是否存在
			upload.GET("/history", uploadHandler.GetUploadHistory)             // 获取上传历史记录
		}
	}
}
