package dto

import (
	"time"
)

// SettlementUploadRequest 结算数据上传请求
type SettlementUploadRequest struct {
	SettlementMonth string                   `json:"settlement_month" binding:"required" example:"2024-01"` // 结算月份 YYYY-MM
	Records         []SettlementUploadRecord `json:"records" binding:"required,dive"`                       // 结算记录列表
	BatchRemark     string                   `json:"batch_remark"`                                          // 批次备注
	OverwriteMode   bool                     `json:"overwrite_mode"`                                        // 是否覆盖已存在记录
}

// SettlementUploadRecord 结算上传记录
type SettlementUploadRecord struct {
	AttendantID    int64   `json:"attendant_id" binding:"required,min=1"`    // 陪诊师ID
	BaseCommission float64 `json:"base_commission" binding:"required,min=0"` // 基础佣金
	TaxAmount      float64 `json:"tax_amount" binding:"min=0"`               // 纳税额度
	ActualAmount   float64 `json:"actual_amount" binding:"required,min=0"`   // 实际到账金额
	OrderCount     int     `json:"order_count" binding:"min=0"`              // 订单数量
	AttendantName  string  `json:"attendant_name"`                           // 陪诊师姓名（用于验证）
	AttendantPhone string  `json:"attendant_phone"`                          // 陪诊师手机号（用于验证）
}

// SettlementUploadResponse 结算数据上传响应
type SettlementUploadResponse struct {
	BatchID         string                         `json:"batch_id"`         // 批次ID
	SettlementMonth string                         `json:"settlement_month"` // 结算月份
	TotalRecords    int                            `json:"total_records"`    // 总记录数
	SuccessCount    int                            `json:"success_count"`    // 成功数量
	FailedCount     int                            `json:"failed_count"`     // 失败数量
	SuccessRecords  []SettlementUploadResult       `json:"success_records"`  // 成功记录
	FailedRecords   []SettlementUploadFailedResult `json:"failed_records"`   // 失败记录
	UploadTime      time.Time                      `json:"upload_time"`      // 上传时间
}

// SettlementUploadResult 结算上传成功结果
type SettlementUploadResult struct {
	SettlementID   int64   `json:"settlement_id"`   // 结算记录ID
	AttendantID    int64   `json:"attendant_id"`    // 陪诊师ID
	AttendantName  string  `json:"attendant_name"`  // 陪诊师姓名
	BaseCommission float64 `json:"base_commission"` // 基础佣金
	TaxAmount      float64 `json:"tax_amount"`      // 纳税额度
	ActualAmount   float64 `json:"actual_amount"`   // 实际到账金额
	OrderCount     int     `json:"order_count"`     // 订单数量
	Status         string  `json:"status"`          // 处理状态
}

// SettlementUploadFailedResult 结算上传失败结果
type SettlementUploadFailedResult struct {
	AttendantID   int64             `json:"attendant_id"`           // 陪诊师ID
	AttendantName string            `json:"attendant_name"`         // 陪诊师姓名
	ErrorCode     string            `json:"error_code"`             // 错误码
	ErrorMessage  string            `json:"error_message"`          // 错误信息
	FieldErrors   map[string]string `json:"field_errors,omitempty"` // 字段错误详情
}

// PaymentUploadRequest 打款记录上传请求
type PaymentUploadRequest struct {
	BatchNo     string                `json:"batch_no" binding:"required"`     // 打款批次号
	PaymentTime time.Time             `json:"payment_time" binding:"required"` // 打款时间
	Records     []PaymentUploadRecord `json:"records" binding:"required,dive"` // 打款记录列表
	Remark      string                `json:"remark"`                          // 批次备注
}

// PaymentUploadRecord 打款上传记录
type PaymentUploadRecord struct {
	SettlementID    int64   `json:"settlement_id" binding:"required,min=1"`  // 结算记录ID
	PaymentAmount   float64 `json:"payment_amount" binding:"required,min=0"` // 打款金额
	AttendantID     int64   `json:"attendant_id"`                            // 陪诊师ID（用于验证）
	AttendantName   string  `json:"attendant_name"`                          // 陪诊师姓名（用于验证）
	SettlementMonth string  `json:"settlement_month"`                        // 结算月份（用于验证）
	Remark          string  `json:"remark"`                                  // 单条记录备注
}

// PaymentUploadResponse 打款记录上传响应
type PaymentUploadResponse struct {
	BatchNo        string                      `json:"batch_no"`        // 批次号
	PaymentTime    time.Time                   `json:"payment_time"`    // 打款时间
	TotalRecords   int                         `json:"total_records"`   // 总记录数
	SuccessCount   int                         `json:"success_count"`   // 成功数量
	FailedCount    int                         `json:"failed_count"`    // 失败数量
	SuccessRecords []PaymentUploadResult       `json:"success_records"` // 成功记录
	FailedRecords  []PaymentUploadFailedResult `json:"failed_records"`  // 失败记录
	UploadTime     time.Time                   `json:"upload_time"`     // 上传时间
}

// PaymentUploadResult 打款上传成功结果
type PaymentUploadResult struct {
	PaymentID     int64     `json:"payment_id"`     // 打款记录ID
	SettlementID  int64     `json:"settlement_id"`  // 结算记录ID
	AttendantID   int64     `json:"attendant_id"`   // 陪诊师ID
	AttendantName string    `json:"attendant_name"` // 陪诊师姓名
	PaymentAmount float64   `json:"payment_amount"` // 打款金额
	PaymentTime   time.Time `json:"payment_time"`   // 打款时间
	Status        string    `json:"status"`         // 处理状态
}

// PaymentUploadFailedResult 打款上传失败结果
type PaymentUploadFailedResult struct {
	SettlementID  int64             `json:"settlement_id"`          // 结算记录ID
	AttendantID   int64             `json:"attendant_id"`           // 陪诊师ID
	AttendantName string            `json:"attendant_name"`         // 陪诊师姓名
	ErrorCode     string            `json:"error_code"`             // 错误码
	ErrorMessage  string            `json:"error_message"`          // 错误信息
	FieldErrors   map[string]string `json:"field_errors,omitempty"` // 字段错误详情
}

// FileUploadRequest 文件上传请求（支持Excel/CSV）
type FileUploadRequest struct {
	UploadType      string `form:"upload_type" binding:"required,oneof=settlement payment"` // 上传类型：settlement 或 payment
	SettlementMonth string `form:"settlement_month"`                                        // 结算月份（结算数据上传时必填）
	BatchNo         string `form:"batch_no"`                                                // 批次号（打款记录上传时必填）
	PaymentTime     string `form:"payment_time"`                                            // 打款时间（打款记录上传时必填）
	OverwriteMode   bool   `form:"overwrite_mode"`                                          // 是否覆盖已存在记录
	Remark          string `form:"remark"`                                                  // 备注
}

// FileUploadResponse 文件上传响应
type FileUploadResponse struct {
	UploadType       string                `json:"upload_type"`                 // 上传类型
	FileName         string                `json:"file_name"`                   // 文件名
	FileSize         int64                 `json:"file_size"`                   // 文件大小
	TotalRows        int                   `json:"total_rows"`                  // 总行数
	ValidRows        int                   `json:"valid_rows"`                  // 有效行数
	InvalidRows      int                   `json:"invalid_rows"`                // 无效行数
	PreviewData      interface{}           `json:"preview_data"`                // 预览数据
	ValidationErrors []FileValidationError `json:"validation_errors,omitempty"` // 验证错误
	UploadTime       time.Time             `json:"upload_time"`                 // 上传时间
}

// FileValidationError 文件验证错误
type FileValidationError struct {
	Row          int    `json:"row"`           // 行号
	Column       string `json:"column"`        // 列名
	Value        string `json:"value"`         // 错误值
	ErrorCode    string `json:"error_code"`    // 错误码
	ErrorMessage string `json:"error_message"` // 错误信息
}

// BatchProcessRequest 批量处理请求
type BatchProcessRequest struct {
	ProcessType string  `json:"process_type" binding:"required,oneof=confirm rollback"` // 处理类型：confirm 确认，rollback 回滚
	BatchID     string  `json:"batch_id" binding:"required"`                            // 批次ID
	RecordIDs   []int64 `json:"record_ids"`                                             // 指定记录ID（可选）
	Reason      string  `json:"reason"`                                                 // 处理原因
}

// BatchProcessResponse 批量处理响应
type BatchProcessResponse struct {
	ProcessType    string    `json:"process_type"`             // 处理类型
	BatchID        string    `json:"batch_id"`                 // 批次ID
	ProcessedCount int       `json:"processed_count"`          // 处理数量
	SuccessCount   int       `json:"success_count"`            // 成功数量
	FailedCount    int       `json:"failed_count"`             // 失败数量
	ProcessTime    time.Time `json:"process_time"`             // 处理时间
	ErrorMessages  []string  `json:"error_messages,omitempty"` // 错误信息
}
