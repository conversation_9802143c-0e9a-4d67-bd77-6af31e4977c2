{"name": "peizhen-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "NODE_OPTIONS='--no-deprecation --no-warnings' vite", "dev:clean": "NODE_OPTIONS='--no-deprecation --no-warnings' vite", "dev:verbose": "vite", "build": "NODE_OPTIONS='--no-deprecation' vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue --ignore-path .gitignore --fix src"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.5.0", "dayjs": "^1.11.9", "echarts": "^5.4.3", "element-plus": "^2.9.7", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "path-browserify": "^1.0.1", "pinia": "^2.1.6", "vee-validate": "^4.11.3", "vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "eslint": "^8.48.0", "eslint-plugin-vue": "^9.17.0", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.77.0", "sass-loader": "^16.0.5", "vite": "^4.4.9"}}