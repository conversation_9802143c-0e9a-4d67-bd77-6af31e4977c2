/**
 * 上传数据验证工具
 * 
 * 提供结算数据和打款记录的数据验证功能
 */

/**
 * 验证结算数据
 * @param {Array} records - 结算记录数组
 * @param {string} settlementMonth - 结算月份
 * @returns {Object} 验证结果
 */
export function validateSettlementData(records, settlementMonth) {
  const errors = []
  const warnings = []
  
  if (!records || records.length === 0) {
    errors.push({
      type: 'general',
      message: '没有找到有效的数据记录'
    })
    return { valid: false, errors, warnings }
  }
  
  records.forEach((record, index) => {
    const rowNum = index + 2 // Excel行号从2开始（第1行是标题）
    
    // 验证陪诊师ID
    if (!record.attendant_id || !Number.isInteger(Number(record.attendant_id))) {
      errors.push({
        row: rowNum,
        field: 'attendant_id',
        message: '陪诊师ID必须是有效的整数'
      })
    }
    
    // 验证陪诊师姓名
    if (!record.attendant_name || typeof record.attendant_name !== 'string' || record.attendant_name.trim().length === 0) {
      errors.push({
        row: rowNum,
        field: 'attendant_name',
        message: '陪诊师姓名不能为空'
      })
    }
    
    // 验证基础佣金
    if (record.base_commission === undefined || record.base_commission === null || isNaN(record.base_commission)) {
      errors.push({
        row: rowNum,
        field: 'base_commission',
        message: '基础佣金必须是有效的数字'
      })
    } else if (record.base_commission < 0) {
      errors.push({
        row: rowNum,
        field: 'base_commission',
        message: '基础佣金不能为负数'
      })
    } else if (record.base_commission > 100000) {
      warnings.push({
        row: rowNum,
        field: 'base_commission',
        message: '基础佣金金额较大，请确认是否正确'
      })
    }
    
    // 验证纳税额度
    if (record.tax_amount === undefined || record.tax_amount === null || isNaN(record.tax_amount)) {
      errors.push({
        row: rowNum,
        field: 'tax_amount',
        message: '纳税额度必须是有效的数字'
      })
    } else if (record.tax_amount < 0) {
      errors.push({
        row: rowNum,
        field: 'tax_amount',
        message: '纳税额度不能为负数'
      })
    }
    
    // 验证实际到账金额
    if (record.actual_amount === undefined || record.actual_amount === null || isNaN(record.actual_amount)) {
      errors.push({
        row: rowNum,
        field: 'actual_amount',
        message: '实际到账金额必须是有效的数字'
      })
    } else if (record.actual_amount < 0) {
      errors.push({
        row: rowNum,
        field: 'actual_amount',
        message: '实际到账金额不能为负数'
      })
    }
    
    // 验证订单数量
    if (record.order_count === undefined || record.order_count === null || !Number.isInteger(Number(record.order_count))) {
      errors.push({
        row: rowNum,
        field: 'order_count',
        message: '订单数量必须是有效的整数'
      })
    } else if (record.order_count < 0) {
      errors.push({
        row: rowNum,
        field: 'order_count',
        message: '订单数量不能为负数'
      })
    }
    
    // 验证金额逻辑关系
    if (!isNaN(record.base_commission) && !isNaN(record.tax_amount) && !isNaN(record.actual_amount)) {
      const expectedActual = record.base_commission - record.tax_amount
      const diff = Math.abs(expectedActual - record.actual_amount)
      if (diff > 0.01) { // 允许0.01的误差
        warnings.push({
          row: rowNum,
          field: 'actual_amount',
          message: `实际到账金额(${record.actual_amount})与计算值(${expectedActual.toFixed(2)})不匹配`
        })
      }
    }
  })
  
  // 检查重复的陪诊师ID
  const attendantIds = records.map(r => r.attendant_id).filter(id => id)
  const duplicateIds = attendantIds.filter((id, index) => attendantIds.indexOf(id) !== index)
  if (duplicateIds.length > 0) {
    errors.push({
      type: 'general',
      message: `发现重复的陪诊师ID: ${[...new Set(duplicateIds)].join(', ')}`
    })
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalRecords: records.length,
      errorCount: errors.length,
      warningCount: warnings.length
    }
  }
}

/**
 * 验证打款记录数据
 * @param {Array} records - 打款记录数组
 * @param {string} batchNo - 批次号
 * @param {string} paymentTime - 打款时间
 * @returns {Object} 验证结果
 */
export function validatePaymentData(records, batchNo, paymentTime) {
  const errors = []
  const warnings = []
  
  if (!records || records.length === 0) {
    errors.push({
      type: 'general',
      message: '没有找到有效的数据记录'
    })
    return { valid: false, errors, warnings }
  }
  
  // 验证批次号
  if (!batchNo || batchNo.trim().length === 0) {
    errors.push({
      type: 'general',
      message: '打款批次号不能为空'
    })
  }
  
  // 验证打款时间
  if (!paymentTime) {
    errors.push({
      type: 'general',
      message: '打款时间不能为空'
    })
  }
  
  records.forEach((record, index) => {
    const rowNum = index + 2 // Excel行号从2开始
    
    // 验证结算记录ID
    if (!record.settlement_id || !Number.isInteger(Number(record.settlement_id))) {
      errors.push({
        row: rowNum,
        field: 'settlement_id',
        message: '结算记录ID必须是有效的整数'
      })
    }
    
    // 验证陪诊师姓名
    if (!record.attendant_name || typeof record.attendant_name !== 'string' || record.attendant_name.trim().length === 0) {
      errors.push({
        row: rowNum,
        field: 'attendant_name',
        message: '陪诊师姓名不能为空'
      })
    }
    
    // 验证打款金额
    if (record.payment_amount === undefined || record.payment_amount === null || isNaN(record.payment_amount)) {
      errors.push({
        row: rowNum,
        field: 'payment_amount',
        message: '打款金额必须是有效的数字'
      })
    } else if (record.payment_amount <= 0) {
      errors.push({
        row: rowNum,
        field: 'payment_amount',
        message: '打款金额必须大于0'
      })
    } else if (record.payment_amount > 100000) {
      warnings.push({
        row: rowNum,
        field: 'payment_amount',
        message: '打款金额较大，请确认是否正确'
      })
    }
    
    // 验证结算月份格式
    if (record.settlement_month && !/^\d{4}-\d{2}$/.test(record.settlement_month)) {
      errors.push({
        row: rowNum,
        field: 'settlement_month',
        message: '结算月份格式应为YYYY-MM'
      })
    }
  })
  
  // 检查重复的结算记录ID
  const settlementIds = records.map(r => r.settlement_id).filter(id => id)
  const duplicateIds = settlementIds.filter((id, index) => settlementIds.indexOf(id) !== index)
  if (duplicateIds.length > 0) {
    errors.push({
      type: 'general',
      message: `发现重复的结算记录ID: ${[...new Set(duplicateIds)].join(', ')}`
    })
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalRecords: records.length,
      errorCount: errors.length,
      warningCount: warnings.length
    }
  }
}

/**
 * 格式化验证错误信息
 * @param {Array} errors - 错误数组
 * @returns {string} 格式化的错误信息
 */
export function formatValidationErrors(errors) {
  if (!errors || errors.length === 0) {
    return ''
  }
  
  return errors.map(error => {
    if (error.type === 'general') {
      return error.message
    } else {
      return `第${error.row}行 ${error.field}: ${error.message}`
    }
  }).join('\n')
}

/**
 * 生成数据摘要
 * @param {Array} records - 数据记录
 * @param {string} type - 数据类型 ('settlement' | 'payment')
 * @returns {Object} 数据摘要
 */
export function generateDataSummary(records, type) {
  if (!records || records.length === 0) {
    return {
      totalRecords: 0,
      totalAmount: 0,
      averageAmount: 0
    }
  }
  
  const summary = {
    totalRecords: records.length
  }
  
  if (type === 'settlement') {
    const totalBaseCommission = records.reduce((sum, record) => sum + (record.base_commission || 0), 0)
    const totalTaxAmount = records.reduce((sum, record) => sum + (record.tax_amount || 0), 0)
    const totalActualAmount = records.reduce((sum, record) => sum + (record.actual_amount || 0), 0)
    const totalOrderCount = records.reduce((sum, record) => sum + (record.order_count || 0), 0)
    
    summary.totalBaseCommission = totalBaseCommission
    summary.totalTaxAmount = totalTaxAmount
    summary.totalActualAmount = totalActualAmount
    summary.totalOrderCount = totalOrderCount
    summary.averageCommission = totalBaseCommission / records.length
    summary.averageOrderCount = totalOrderCount / records.length
  } else if (type === 'payment') {
    const totalPaymentAmount = records.reduce((sum, record) => sum + (record.payment_amount || 0), 0)
    
    summary.totalPaymentAmount = totalPaymentAmount
    summary.averagePaymentAmount = totalPaymentAmount / records.length
  }
  
  return summary
}

/**
 * 检查数据完整性
 * @param {Array} records - 数据记录
 * @param {Array} requiredFields - 必填字段
 * @returns {Object} 完整性检查结果
 */
export function checkDataIntegrity(records, requiredFields) {
  const missingFields = []
  const incompleteRecords = []
  
  records.forEach((record, index) => {
    const missing = []
    requiredFields.forEach(field => {
      if (record[field] === undefined || record[field] === null || record[field] === '') {
        missing.push(field)
      }
    })
    
    if (missing.length > 0) {
      incompleteRecords.push({
        row: index + 2,
        missingFields: missing
      })
    }
  })
  
  return {
    isComplete: incompleteRecords.length === 0,
    incompleteRecords,
    completionRate: ((records.length - incompleteRecords.length) / records.length * 100).toFixed(2)
  }
}