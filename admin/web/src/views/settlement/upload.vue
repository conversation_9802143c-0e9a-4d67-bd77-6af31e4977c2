<template>
  <div class="settlement-upload">
    <div class="page-header">
      <h2>结算数据上传</h2>
      <p>上传每月的结算明细数据和打款订单数据</p>
    </div>

    <!-- 上传类型选择 -->
    <el-card class="upload-type-card">
      <template #header>
        <div class="card-header">
          <span>选择上传类型</span>
        </div>
      </template>
      
      <el-radio-group v-model="uploadType" @change="handleTypeChange">
        <el-radio-button label="settlement">结算明细数据</el-radio-button>
        <el-radio-button label="payment">打款记录数据</el-radio-button>
      </el-radio-group>
    </el-card>

    <!-- 结算数据上传 -->
    <el-card v-if="uploadType === 'settlement'" class="upload-card">
      <template #header>
        <div class="card-header">
          <span>结算明细数据上传</span>
          <div class="header-actions">
            <el-button @click="downloadTemplate('settlement')" type="info" size="small">
              下载模板
            </el-button>
            <el-button @click="clearData" size="small">清空数据</el-button>
          </div>
        </div>
      </template>

      <div class="upload-section">
        <!-- 基本信息 -->
        <el-form :model="settlementForm" :rules="settlementRules" ref="settlementFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="结算月份" prop="settlement_month">
                <el-date-picker
                  v-model="settlementForm.settlement_month"
                  type="month"
                  placeholder="选择结算月份"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="批次备注" prop="batch_remark">
                <el-input
                  v-model="settlementForm.batch_remark"
                  placeholder="请输入批次备注信息"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 文件上传 -->
        <div class="file-upload-section">
          <el-upload
            ref="settlementUploadRef"
            class="upload-demo"
            drag
            :action="uploadAction"
            :headers="uploadHeaders"
            :data="uploadData"
            :on-change="handleSettlementFileChange"
            :on-success="handleSettlementUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeSettlementUpload"
            :auto-upload="false"
            accept=".xlsx,.xls,.csv"
            :limit="1"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 .xlsx, .xls, .csv 格式文件，文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </div>

        <!-- 数据预览 -->
        <DataPreview
          v-if="settlementPreviewData.length > 0"
          :data="settlementPreviewData"
          :columns="settlementColumns"
          :errors="validationErrors"
          :warnings="validationWarnings"
          :summary="settlementSummary"
          title="结算数据预览"
        >
          <template #actions>
            <el-button @click="validateData" :loading="validating" type="warning" size="small">
              验证数据
            </el-button>
            <el-button @click="submitSettlementData" :loading="uploading" type="primary" size="small">
              确认上传
            </el-button>
          </template>
          <template #base_commission="{ row }">
            ¥{{ row.base_commission.toFixed(2) }}
          </template>
          <template #tax_amount="{ row }">
            ¥{{ row.tax_amount.toFixed(2) }}
          </template>
          <template #actual_amount="{ row }">
            ¥{{ row.actual_amount.toFixed(2) }}
          </template>
        </DataPreview>


      </div>
    </el-card>

    <!-- 打款记录上传 -->
    <el-card v-if="uploadType === 'payment'" class="upload-card">
      <template #header>
        <div class="card-header">
          <span>打款记录数据上传</span>
          <div class="header-actions">
            <el-button @click="downloadTemplate('payment')" type="info" size="small">
              下载模板
            </el-button>
            <el-button @click="clearData" size="small">清空数据</el-button>
          </div>
        </div>
      </template>

      <div class="upload-section">
        <!-- 基本信息 -->
        <el-form :model="paymentForm" :rules="paymentRules" ref="paymentFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="打款批次号" prop="batch_no">
                <el-input
                  v-model="paymentForm.batch_no"
                  placeholder="请输入打款批次号"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="打款时间" prop="payment_time">
                <el-date-picker
                  v-model="paymentForm.payment_time"
                  type="datetime"
                  placeholder="选择打款时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="批次备注" prop="remark">
                <el-input
                  v-model="paymentForm.remark"
                  placeholder="请输入批次备注"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 文件上传 -->
        <div class="file-upload-section">
          <el-upload
            ref="paymentUploadRef"
            class="upload-demo"
            drag
            :action="uploadAction"
            :headers="uploadHeaders"
            :data="uploadData"
            :on-change="handlePaymentFileChange"
            :on-success="handlePaymentUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforePaymentUpload"
            :auto-upload="false"
            accept=".xlsx,.xls,.csv"
            :limit="1"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 .xlsx, .xls, .csv 格式文件，文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </div>

        <!-- 数据预览 -->
        <DataPreview
          v-if="paymentPreviewData.length > 0"
          :data="paymentPreviewData"
          :columns="paymentColumns"
          :errors="validationErrors"
          :warnings="validationWarnings"
          :summary="paymentSummary"
          title="打款记录预览"
        >
          <template #actions>
            <el-button @click="validatePaymentData" :loading="validating" type="warning" size="small">
              验证数据
            </el-button>
            <el-button @click="submitPaymentData" :loading="uploading" type="primary" size="small">
              确认上传
            </el-button>
          </template>
          <template #payment_amount="{ row }">
            ¥{{ row.payment_amount.toFixed(2) }}
          </template>
        </DataPreview>
      </div>
    </el-card>

    <!-- 上传进度 -->
    <el-dialog
      v-model="progressDialogVisible"
      title="上传进度"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="progress-content">
        <el-progress
          :percentage="uploadProgress"
          :status="uploadStatus"
          :stroke-width="8"
        />
        <div class="progress-text">
          {{ progressText }}
        </div>
      </div>
      <template #footer>
        <el-button @click="progressDialogVisible = false" :disabled="uploading">
          关闭
        </el-button>
      </template>
    </el-dialog>

    <!-- 上传结果对话框 -->
    <UploadResultDialog
      v-model="resultDialogVisible"
      :title="resultDialogTitle"
      :status="resultStatus"
      :success-count="resultSuccessCount"
      :error-count="resultErrorCount"
      :total-amount="resultTotalAmount"
      :error-message="resultErrorMessage"
      :details="resultDetails"
      :suggestions="resultSuggestions"
      :show-retry="resultShowRetry"
      @retry="handleResultRetry"
      @view-history="loadUploadHistory"
    />

    <!-- 上传历史 -->
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <span>上传历史</span>
          <el-button @click="loadUploadHistory" :loading="loadingHistory" size="small">
            刷新
          </el-button>
        </div>
      </template>

      <el-table
        :data="uploadHistoryData"
        v-loading="loadingHistory"
        style="width: 100%"
      >
        <el-table-column prop="upload_type" label="上传类型" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.upload_type === 'settlement' ? 'primary' : 'success'">
              {{ scope.row.upload_type === 'settlement' ? '结算数据' : '打款记录' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="batch_id" label="批次ID" width="150" />
        <el-table-column prop="file_name" label="文件名" />
        <el-table-column prop="record_count" label="记录数" width="100" />
        <el-table-column prop="success_count" label="成功数" width="100" />
        <el-table-column prop="error_count" label="失败数" width="100" />
        <el-table-column prop="operator_name" label="操作人" width="120" />
        <el-table-column prop="created_at" label="上传时间" width="180" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              @click="viewUploadDetail(scope.row)"
              type="text"
              size="small"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="historyPagination.page"
          v-model:page-size="historyPagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="historyPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { UploadFilled } from '@element-plus/icons-vue'
import { uploadSettlementData, uploadPaymentRecords, getUploadHistory, validateUploadData } from '@/api/settlement'
import { getToken } from '@/utils/auth'
import * as XLSX from 'xlsx'
import DataPreview from '@/components/DataPreview.vue'
import UploadResultDialog from '@/components/UploadResultDialog.vue'
import { validateSettlementData, validatePaymentData, generateDataSummary } from '@/utils/upload-validator'

export default {
  name: 'SettlementUpload',
  components: {
    UploadFilled,
    DataPreview,
    UploadResultDialog
  },
  data() {
    return {
      uploadType: 'settlement',
      
      // 结算数据表单
      settlementForm: {
        settlement_month: '',
        batch_remark: ''
      },
      settlementRules: {
        settlement_month: [
          { required: true, message: '请选择结算月份', trigger: 'change' }
        ]
      },
      
      // 打款记录表单
      paymentForm: {
        batch_no: '',
        payment_time: '',
        remark: ''
      },
      paymentRules: {
        batch_no: [
          { required: true, message: '请输入打款批次号', trigger: 'blur' }
        ],
        payment_time: [
          { required: true, message: '请选择打款时间', trigger: 'change' }
        ]
      },
      
      // 上传相关
      uploadAction: '/api/admin/upload/parse',
      uploadHeaders: {
        'Authorization': 'Bearer ' + getToken()
      },
      uploadData: {},
      
      // 预览数据
      settlementPreviewData: [],
      paymentPreviewData: [],
      
      // 验证错误
      validationErrors: [],
      validationWarnings: [],
      
      // 表格列定义
      settlementColumns: [
        { prop: 'attendant_id', label: '陪诊师ID', width: 100 },
        { prop: 'attendant_name', label: '陪诊师姓名', width: 120 },
        { prop: 'base_commission', label: '基础佣金', width: 120, slot: 'base_commission' },
        { prop: 'tax_amount', label: '纳税额度', width: 120, slot: 'tax_amount' },
        { prop: 'actual_amount', label: '实际到账', width: 120, slot: 'actual_amount' },
        { prop: 'order_count', label: '订单数量', width: 100 }
      ],
      paymentColumns: [
        { prop: 'settlement_id', label: '结算记录ID', width: 120 },
        { prop: 'attendant_name', label: '陪诊师姓名', width: 120 },
        { prop: 'payment_amount', label: '打款金额', width: 120, slot: 'payment_amount' },
        { prop: 'settlement_month', label: '结算月份', width: 100 },
        { prop: 'remark', label: '备注', minWidth: 150 }
      ],
      
      // 状态控制
      uploading: false,
      validating: false,
      progressDialogVisible: false,
      uploadProgress: 0,
      uploadStatus: '',
      progressText: '',
      
      // 上传历史
      uploadHistoryData: [],
      loadingHistory: false,
      historyPagination: {
        page: 1,
        size: 20,
        total: 0
      },
      
      // 结果对话框
      resultDialogVisible: false,
      resultDialogTitle: '上传结果',
      resultStatus: 'success',
      resultSuccessCount: 0,
      resultErrorCount: 0,
      resultTotalAmount: 0,
      resultErrorMessage: '',
      resultDetails: [],
      resultSuggestions: [],
      resultShowRetry: false
    }
  },
  computed: {
    settlementSummary() {
      return generateDataSummary(this.settlementPreviewData, 'settlement')
    },
    paymentSummary() {
      return generateDataSummary(this.paymentPreviewData, 'payment')
    }
  },
  created() {
    this.loadUploadHistory()
  },
  methods: {
    // 切换上传类型
    handleTypeChange() {
      this.clearData()
    },
    
    // 清空数据
    clearData() {
      this.settlementPreviewData = []
      this.paymentPreviewData = []
      this.validationErrors = []
      this.validationWarnings = []
      this.settlementForm = {
        settlement_month: '',
        batch_remark: ''
      }
      this.paymentForm = {
        batch_no: '',
        payment_time: '',
        remark: ''
      }
      
      // 清空上传组件
      if (this.$refs.settlementUploadRef) {
        this.$refs.settlementUploadRef.clearFiles()
      }
      if (this.$refs.paymentUploadRef) {
        this.$refs.paymentUploadRef.clearFiles()
      }
    },
    
    // 下载模板
    downloadTemplate(type) {
      const templates = {
        settlement: {
          filename: '结算明细数据模板.xlsx',
          headers: ['陪诊师ID', '陪诊师姓名', '基础佣金', '纳税额度', '实际到账金额', '订单数量'],
          data: [
            [1001, '张三', 500.00, 30.00, 470.00, 5],
            [1002, '李四', 800.00, 48.00, 752.00, 8]
          ]
        },
        payment: {
          filename: '打款记录数据模板.xlsx',
          headers: ['结算记录ID', '陪诊师姓名', '打款金额', '结算月份', '备注'],
          data: [
            [1, '张三', 470.00, '2024-01', '正常打款'],
            [2, '李四', 752.00, '2024-01', '正常打款']
          ]
        }
      }
      
      const template = templates[type]
      const ws = XLSX.utils.aoa_to_sheet([template.headers, ...template.data])
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')
      XLSX.writeFile(wb, template.filename)
    },
    
    // 结算数据文件变化
    handleSettlementFileChange(file, fileList) {
      if (file.status === 'ready') {
        this.parseExcelFile(file.raw, 'settlement')
      }
    },
    
    // 打款记录文件变化
    handlePaymentFileChange(file, fileList) {
      if (file.status === 'ready') {
        this.parseExcelFile(file.raw, 'payment')
      }
    },
    
    // 解析Excel文件
    parseExcelFile(file, type) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result)
          const workbook = XLSX.read(data, { type: 'array' })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
          
          if (type === 'settlement') {
            this.parseSettlementData(jsonData)
          } else {
            this.parsePaymentData(jsonData)
          }
        } catch (error) {
          this.$message.error('文件解析失败: ' + error.message)
        }
      }
      reader.readAsArrayBuffer(file)
    },
    
    // 解析结算数据
    parseSettlementData(jsonData) {
      if (jsonData.length < 2) {
        this.$message.error('文件数据为空')
        return
      }
      
      const headers = jsonData[0]
      const rows = jsonData.slice(1)
      
      this.settlementPreviewData = rows.map((row, index) => ({
        attendant_id: row[0],
        attendant_name: row[1],
        base_commission: parseFloat(row[2]) || 0,
        tax_amount: parseFloat(row[3]) || 0,
        actual_amount: parseFloat(row[4]) || 0,
        order_count: parseInt(row[5]) || 0,
        _status: 'pending',
        _rowIndex: index + 2
      })).filter(item => item.attendant_id)
      
      this.$message.success(`成功解析 ${this.settlementPreviewData.length} 条结算记录`)
    },
    
    // 解析打款数据
    parsePaymentData(jsonData) {
      if (jsonData.length < 2) {
        this.$message.error('文件数据为空')
        return
      }
      
      const headers = jsonData[0]
      const rows = jsonData.slice(1)
      
      this.paymentPreviewData = rows.map((row, index) => ({
        settlement_id: parseInt(row[0]) || 0,
        attendant_name: row[1],
        payment_amount: parseFloat(row[2]) || 0,
        settlement_month: row[3],
        remark: row[4] || '',
        _status: 'pending',
        _rowIndex: index + 2
      })).filter(item => item.settlement_id)
      
      this.$message.success(`成功解析 ${this.paymentPreviewData.length} 条打款记录`)
    },
    
    // 验证数据
    async validateData() {
      if (this.settlementPreviewData.length === 0) {
        this.$message.warning('请先上传文件')
        return
      }
      
      try {
        this.validating = true
        
        // 客户端验证
        const clientValidation = validateSettlementData(this.settlementPreviewData, this.settlementForm.settlement_month)
        this.validationErrors = clientValidation.errors
        this.validationWarnings = clientValidation.warnings
        
        // 服务端验证
        const response = await validateUploadData({
          type: 'settlement',
          settlement_month: this.settlementForm.settlement_month,
          records: this.settlementPreviewData
        })
        
        if (response.code === 0) {
          // 合并服务端验证结果
          if (response.data.errors) {
            this.validationErrors = [...this.validationErrors, ...response.data.errors]
          }
          if (response.data.warnings) {
            this.validationWarnings = [...this.validationWarnings, ...response.data.warnings]
          }
          
          if (this.validationErrors.length === 0) {
            this.$message.success('数据验证通过')
          } else {
            this.$message.error(`数据验证失败，发现 ${this.validationErrors.length} 个错误`)
          }
        }
      } catch (error) {
        this.$message.error('验证失败: ' + error.message)
      } finally {
        this.validating = false
      }
    },
    
    // 验证打款数据
    async validatePaymentData() {
      if (this.paymentPreviewData.length === 0) {
        this.$message.warning('请先上传文件')
        return
      }
      
      try {
        this.validating = true
        
        // 客户端验证
        const clientValidation = validatePaymentData(this.paymentPreviewData, this.paymentForm.batch_no, this.paymentForm.payment_time)
        this.validationErrors = clientValidation.errors
        this.validationWarnings = clientValidation.warnings
        
        // 服务端验证
        const response = await validateUploadData({
          type: 'payment',
          batch_no: this.paymentForm.batch_no,
          payment_time: this.paymentForm.payment_time,
          records: this.paymentPreviewData
        })
        
        if (response.code === 0) {
          // 合并服务端验证结果
          if (response.data.errors) {
            this.validationErrors = [...this.validationErrors, ...response.data.errors]
          }
          if (response.data.warnings) {
            this.validationWarnings = [...this.validationWarnings, ...response.data.warnings]
          }
          
          if (this.validationErrors.length === 0) {
            this.$message.success('数据验证通过')
          } else {
            this.$message.error(`数据验证失败，发现 ${this.validationErrors.length} 个错误`)
          }
        }
      } catch (error) {
        this.$message.error('验证失败: ' + error.message)
      } finally {
        this.validating = false
      }
    },
    
    // 提交结算数据
    async submitSettlementData() {
      if (this.validationErrors.length > 0) {
        this.$message.error('请先修复数据验证错误')
        return
      }
      
      try {
        await this.$refs.settlementFormRef.validate()
        
        this.uploading = true
        this.progressDialogVisible = true
        this.uploadProgress = 0
        this.uploadStatus = ''
        this.progressText = '正在上传结算数据...'
        
        const response = await uploadSettlementData({
          settlement_month: this.settlementForm.settlement_month,
          batch_remark: this.settlementForm.batch_remark,
          records: this.settlementPreviewData
        })
        
        if (response.code === 0) {
          this.uploadProgress = 100
          this.uploadStatus = 'success'
          this.progressText = '上传成功！'
          
          // 显示结果对话框
          this.showUploadResult({
            status: 'success',
            title: '结算数据上传成功',
            successCount: response.data.success_count || this.settlementPreviewData.length,
            totalAmount: this.settlementSummary.totalActualAmount,
            details: response.data.details || []
          })
          
          this.clearData()
          this.loadUploadHistory()
        } else {
          this.uploadStatus = 'exception'
          this.progressText = '上传失败: ' + response.message
          
          this.showUploadResult({
            status: 'error',
            title: '结算数据上传失败',
            errorMessage: response.message || '上传失败',
            showRetry: true
          })
        }
      } catch (error) {
        this.uploadStatus = 'exception'
        this.progressText = '上传失败: ' + error.message
        
        this.showUploadResult({
          status: 'error',
          title: '结算数据上传失败',
          errorMessage: error.message,
          showRetry: true,
          suggestions: [
            '请检查网络连接是否正常',
            '确认数据格式是否正确',
            '如问题持续存在，请联系系统管理员'
          ]
        })
      } finally {
        this.uploading = false
      }
    },
    
    // 提交打款数据
    async submitPaymentData() {
      if (this.validationErrors.length > 0) {
        this.$message.error('请先修复数据验证错误')
        return
      }
      
      try {
        await this.$refs.paymentFormRef.validate()
        
        this.uploading = true
        this.progressDialogVisible = true
        this.uploadProgress = 0
        this.uploadStatus = ''
        this.progressText = '正在上传打款记录...'
        
        const response = await uploadPaymentRecords({
          batch_no: this.paymentForm.batch_no,
          payment_time: this.paymentForm.payment_time,
          remark: this.paymentForm.remark,
          records: this.paymentPreviewData
        })
        
        if (response.code === 0) {
          this.uploadProgress = 100
          this.uploadStatus = 'success'
          this.progressText = '上传成功！'
          
          // 显示结果对话框
          this.showUploadResult({
            status: 'success',
            title: '打款记录上传成功',
            successCount: response.data.success_count || this.paymentPreviewData.length,
            totalAmount: this.paymentSummary.totalPaymentAmount,
            details: response.data.details || []
          })
          
          this.clearData()
          this.loadUploadHistory()
        } else {
          this.uploadStatus = 'exception'
          this.progressText = '上传失败: ' + response.message
          
          this.showUploadResult({
            status: 'error',
            title: '打款记录上传失败',
            errorMessage: response.message || '上传失败',
            showRetry: true
          })
        }
      } catch (error) {
        this.uploadStatus = 'exception'
        this.progressText = '上传失败: ' + error.message
        
        this.showUploadResult({
          status: 'error',
          title: '打款记录上传失败',
          errorMessage: error.message,
          showRetry: true,
          suggestions: [
            '请检查网络连接是否正常',
            '确认数据格式是否正确',
            '如问题持续存在，请联系系统管理员'
          ]
        })
      } finally {
        this.uploading = false
      }
    },
    
    // 上传前检查
    beforeSettlementUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel' ||
                     file.type === 'text/csv'
      const isLt10M = file.size / 1024 / 1024 < 10
      
      if (!isExcel) {
        this.$message.error('只能上传 Excel 或 CSV 文件!')
        return false
      }
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!')
        return false
      }
      return false // 阻止自动上传，使用手动解析
    },
    
    // 上传前检查
    beforePaymentUpload(file) {
      return this.beforeSettlementUpload(file)
    },
    
    // 上传成功
    handleSettlementUploadSuccess(response, file, fileList) {
      // 由于使用手动解析，这里不会被调用
    },
    
    // 上传成功
    handlePaymentUploadSuccess(response, file, fileList) {
      // 由于使用手动解析，这里不会被调用
    },
    
    // 上传失败
    handleUploadError(error, file, fileList) {
      this.$message.error('文件上传失败: ' + error.message)
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'success': 'success',
        'failed': 'danger',
        'processing': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待处理',
        'success': '成功',
        'failed': '失败',
        'processing': '处理中'
      }
      return statusMap[status] || '未知'
    },
    
    // 加载上传历史
    async loadUploadHistory() {
      try {
        this.loadingHistory = true
        const response = await getUploadHistory({
          page: this.historyPagination.page,
          size: this.historyPagination.size
        })
        
        if (response.code === 0) {
          this.uploadHistoryData = response.data.records || []
          this.historyPagination.total = response.data.total || 0
        }
      } catch (error) {
        this.$message.error('加载上传历史失败: ' + error.message)
      } finally {
        this.loadingHistory = false
      }
    },
    
    // 分页大小变化
    handleHistorySizeChange(size) {
      this.historyPagination.size = size
      this.historyPagination.page = 1
      this.loadUploadHistory()
    },
    
    // 当前页变化
    handleHistoryCurrentChange(page) {
      this.historyPagination.page = page
      this.loadUploadHistory()
    },
    
    // 查看上传详情
    viewUploadDetail(row) {
      // TODO: 实现查看详情功能
      this.$message.info('查看详情功能开发中...')
    },
    
    // 显示上传结果
    showUploadResult(options) {
      this.resultDialogTitle = options.title || '上传结果'
      this.resultStatus = options.status || 'success'
      this.resultSuccessCount = options.successCount || 0
      this.resultErrorCount = options.errorCount || 0
      this.resultTotalAmount = options.totalAmount || 0
      this.resultErrorMessage = options.errorMessage || ''
      this.resultDetails = options.details || []
      this.resultSuggestions = options.suggestions || []
      this.resultShowRetry = options.showRetry || false
      
      // 延迟显示结果对话框，让进度对话框先关闭
      setTimeout(() => {
        this.progressDialogVisible = false
        this.resultDialogVisible = true
      }, 500)
    },
    
    // 处理重试
    handleResultRetry() {
      if (this.uploadType === 'settlement') {
        this.submitSettlementData()
      } else {
        this.submitPaymentData()
      }
    }
  }
}
</script>

<style scoped>
.settlement-upload {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.upload-type-card,
.upload-card,
.history-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.upload-section {
  padding: 20px 0;
}

.file-upload-section {
  margin: 20px 0;
}

.upload-demo {
  width: 100%;
}



.progress-content {
  text-align: center;
  padding: 20px;
}

.progress-text {
  margin-top: 15px;
  color: #606266;
  font-size: 14px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}

.el-radio-group {
  margin-bottom: 20px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 7px;
}
</style>