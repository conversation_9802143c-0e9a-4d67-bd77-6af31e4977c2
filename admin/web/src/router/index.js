import { createRouter, createWebHistory } from 'vue-router'

// 布局组件
const Layout = () => import('@/layouts/index.vue')

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/dashboard',
    component: Layout,
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { 
          title: '仪表盘', 
          icon: 'Odometer',
          affix: true,
          breadcrumb: [{ title: '首页', path: '/dashboard' }]
        }
      }
    ]
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/manual-processing',
    component: Layout,
    name: 'ManualProcessing',
    meta: { 
      title: '人工处理', 
      icon: 'Tools',
      permissions: ['manual:view']
    },
    children: [
      {
        path: 'workbench',
        name: 'ManualProcessingWorkbench',
        component: () => import('@/views/manual-processing/workbench.vue'),
        meta: { 
          title: '处理工作台',
          permissions: ['manual:workbench'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '人工处理', path: '/manual-processing' },
            { title: '处理工作台', path: '/manual-processing/workbench' }
          ]
        }
      }
    ]
  },
  {
    path: '/monitoring',
    component: Layout,
    name: 'Monitoring',
    meta: { 
      title: '系统监控', 
      icon: 'Monitor',
      permissions: ['monitoring:view']
    },
    children: [
      {
        path: 'dashboard',
        name: 'MonitoringDashboard',
        component: () => import('@/views/monitoring/dashboard.vue'),
        meta: { 
          title: '监控面板',
          permissions: ['monitoring:dashboard'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '系统监控', path: '/monitoring' },
            { title: '监控面板', path: '/monitoring/dashboard' }
          ]
        }
      },
      {
        path: 'alerts',
        name: 'MonitoringAlerts',
        component: () => import('@/views/monitoring/alerts.vue'),
        meta: { 
          title: '告警管理',
          permissions: ['monitoring:alerts'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '系统监控', path: '/monitoring' },
            { title: '告警管理', path: '/monitoring/alerts' }
          ]
        }
      }
    ]
  },
  {
    path: '/service',
    component: Layout,
    name: 'Service',
    meta: { 
      title: '服务管理', 
      icon: 'SetUp',
      permissions: ['service:view']
    },
    children: [
      {
        path: 'list',
        name: 'ServiceList',
        component: () => import('@/views/service/ServiceList.vue'),
        meta: { 
          title: '服务列表',
          permissions: ['service:list'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '服务管理', path: '/service' },
            { title: '服务列表', path: '/service/list' }
          ]
        }
      },
      {
        path: 'statistics',
        name: 'ServiceStatistics',
        component: () => import('@/views/service/ServiceStatistics.vue'),
        meta: { 
          title: '服务统计',
          permissions: ['service:statistics'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '服务管理', path: '/service' },
            { title: '服务统计', path: '/service/statistics' }
          ]
        }
      },
      {
        path: 'create',
        name: 'ServiceCreate',
        component: () => import('@/views/service/ServiceEdit.vue'),
        meta: { 
          title: '新增服务', 
          hidden: true,
          permissions: ['service:create'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '服务管理', path: '/service' },
            { title: '服务列表', path: '/service/list' },
            { title: '新增服务', path: '/service/create' }
          ]
        }
      },
      {
        path: 'edit/:id',
        name: 'ServiceEdit',
        component: () => import('@/views/service/ServiceEdit.vue'),
        meta: { 
          title: '编辑服务', 
          hidden: true,
          permissions: ['service:edit'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '服务管理', path: '/service' },
            { title: '服务列表', path: '/service/list' },
            { title: '编辑服务', path: '' }
          ]
        }
      },
      {
        path: 'detail/:id',
        name: 'ServiceDetail',
        component: () => import('@/views/service/ServiceDetail.vue'),
        meta: { 
          title: '服务详情', 
          hidden: true,
          permissions: ['service:view'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '服务管理', path: '/service' },
            { title: '服务列表', path: '/service/list' },
            { title: '服务详情', path: '' }
          ]
        }
      }
    ]
  },
  {
    path: '/attendant',
    component: Layout,
    name: 'Attendant',
    meta: { 
      title: '陪诊师管理', 
      icon: 'User',
      permissions: ['attendant:view']
    },
    children: [
      {
        path: 'list',
        name: 'AttendantList',
        component: () => import('@/views/attendant/list.vue'),
        meta: { 
          title: '陪诊师列表',
          permissions: ['attendant:list'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '陪诊师管理', path: '/attendant' },
            { title: '陪诊师列表', path: '/attendant/list' }
          ]
        }
      },
      {
        path: 'verification',
        name: 'AttendantVerification',
        component: () => import('@/views/attendant/verification.vue'),
        meta: { 
          title: '认证审核',
          permissions: ['attendant:verification'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '陪诊师管理', path: '/attendant' },
            { title: '认证审核', path: '/attendant/verification' }
          ]
        }
      },
      {
        path: 'service',
        name: 'AttendantService',
        component: () => import('@/views/attendant/service.vue'),
        meta: { 
          title: '服务管理',
          permissions: ['attendant:service'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '陪诊师管理', path: '/attendant' },
            { title: '服务管理', path: '/attendant/service' }
          ]
        }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    name: 'User',
    meta: { 
      title: '用户管理', 
      icon: 'UserFilled',
      permissions: ['user:view']
    },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('@/views/user/list.vue'),
        meta: { 
          title: '用户列表',
          permissions: ['user:list'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '用户管理', path: '/user' },
            { title: '用户列表', path: '/user/list' }
          ]
        }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    name: 'Order',
    meta: { 
      title: '订单管理', 
      icon: 'Document',
      permissions: ['order:view']
    },
    children: [
      {
        path: 'list',
        name: 'OrderList',
        component: () => import('@/views/order/list.vue'),
        meta: { 
          title: '订单列表',
          permissions: ['order:list'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '订单管理', path: '/order' },
            { title: '订单列表', path: '/order/list' }
          ]
        }
      },
      {
        path: 'matching',
        name: 'OrderMatching',
        component: () => import('@/views/order/MatchingList.vue'),
        meta: { 
          title: '陪诊师匹配',
          permissions: ['order:matching'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '订单管理', path: '/order' },
            { title: '陪诊师匹配', path: '/order/matching' }
          ]
        }
      },
      {
        path: 'matching/detail/:id',
        name: 'OrderMatchingDetail',
        component: () => import('@/views/order/MatchingDetail.vue'),
        meta: { 
          title: '匹配详情', 
          hidden: true,
          permissions: ['order:matching'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '订单管理', path: '/order' },
            { title: '陪诊师匹配', path: '/order/matching' },
            { title: '匹配详情', path: '' }
          ]
        }
      },
      {
        path: 'matching/emergency-pool',
        name: 'OrderEmergencyPool',
        component: () => import('@/views/order/EmergencyPool.vue'),
        meta: { 
          title: '紧急处理池',
          permissions: ['order:emergency'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '订单管理', path: '/order' },
            { title: '陪诊师匹配', path: '/order/matching' },
            { title: '紧急处理池', path: '/order/matching/emergency-pool' }
          ]
        }
      },
      {
        path: 'detail/:id',
        name: 'OrderDetail',
        component: () => import('@/views/order/detail.vue'),
        meta: {
          title: '订单详情',
          hidden: true,
          permissions: ['order:view'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '订单管理', path: '/order' },
            { title: '订单列表', path: '/order/list' },
            { title: '订单详情', path: '' }
          ]
        }
      },
      {
        path: 'review',
        name: 'OrderReview',
        component: () => import('@/views/order/ReviewManagement.vue'),
        meta: {
          title: '服务成果审核',
          permissions: ['order:review'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '订单管理', path: '/order' },
            { title: '服务成果审核', path: '/order/review' }
          ]
        }
      }
    ]
  },
  {
    path: '/approval',
    component: Layout,
    name: 'Approval',
    meta: { 
      title: '审批管理', 
      icon: 'Check',
      permissions: ['approval:view']
    },
    children: [
      {
        path: 'unified',
        name: 'UnifiedApproval',
        component: () => import('@/views/approval/unified-approval.vue'),
        meta: { 
          title: '审批管理',
          permissions: ['approval:manage'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '审批管理', path: '/approval' },
            { title: '审批管理', path: '/approval/unified' }
          ]
        }
      },
      {
        path: 'rules',
        name: 'ApprovalRules',
        component: () => import('@/views/approval/rules.vue'),
        meta: { 
          title: '审批规则',
          permissions: ['approval:rules'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '审批管理', path: '/approval' },
            { title: '审批规则', path: '/approval/rules' }
          ]
        }
      },
      {
        path: 'statistics',
        name: 'ApprovalStatistics',
        component: () => import('@/views/approval/statistics.vue'),
        meta: { 
          title: '审批统计',
          permissions: ['approval:statistics'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '审批管理', path: '/approval' },
            { title: '审批统计', path: '/approval/statistics' }
          ]
        }
      },
      {
        path: 'pending',
        name: 'ApprovalPending',
        component: () => import('@/views/approval/index.vue'),
        meta: { 
          title: '待审批（旧版）',
          permissions: ['approval:pending'],
          hidden: true, // 隐藏旧版本页面
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '审批管理', path: '/approval' },
            { title: '待审批', path: '/approval/pending' }
          ]
        }
      }
    ]
  },
  {
    path: '/refund-management',
    component: Layout,
    name: 'RefundManagement',
    meta: { 
      title: '退款管理', 
      icon: 'Money',
      permissions: ['refund:view'],
      hidden: true // 已合并到审批管理，暂时隐藏
    },
    children: [
      {
        path: '',
        name: 'RefundManagementIndex',
        component: () => import('@/views/refund-management/index.vue'),
        meta: { 
          title: '退款管理（旧版）',
          permissions: ['refund:list'],
          hidden: true, // 已合并到审批管理
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '退款管理', path: '/refund-management' }
          ]
        }
      }
    ]
  },
  {
    path: '/financial',
    component: Layout,
    name: 'Financial',
    meta: { 
      title: '财务管理', 
      icon: 'Money',
      permissions: ['financial:view']
    },
    children: [
      {
        path: 'dashboard',
        name: 'FinancialDashboard',
        component: () => import('@/views/financial/Dashboard.vue'),
        meta: { 
          title: '财务仪表板',
          permissions: ['financial:dashboard'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '财务管理', path: '/financial' },
            { title: '财务仪表板', path: '/financial/dashboard' }
          ]
        }
      },
      {
        path: 'withdrawal',
        name: 'WithdrawalManagement',
        component: () => import('@/views/financial/WithdrawalManagement.vue'),
        meta: { 
          title: '提现管理',
          permissions: ['financial:withdrawal'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '财务管理', path: '/financial' },
            { title: '提现管理', path: '/financial/withdrawal' }
          ]
        }
      },
      {
        path: 'commission',
        name: 'CommissionManagement',
        component: () => import('@/views/financial/Commission.vue'),
        meta: { 
          title: '佣金管理',
          permissions: ['financial:commission'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '财务管理', path: '/financial' },
            { title: '佣金管理', path: '/financial/commission' }
          ]
        }
      },
      {
        path: 'settlement',
        name: 'SettlementManagement',
        component: () => import('@/views/financial/Settlement.vue'),
        meta: { 
          title: '结算管理',
          permissions: ['financial:settlement'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '财务管理', path: '/financial' },
            { title: '结算管理', path: '/financial/settlement' }
          ]
        }
      }
    ]
  },
  {
    path: '/settlement',
    component: Layout,
    name: 'Settlement',
    meta: { 
      title: '结算系统', 
      icon: 'CreditCard',
      permissions: ['settlement:view']
    },
    children: [
      {
        path: 'config',
        name: 'SettlementConfig',
        component: () => import('@/views/settlement/config.vue'),
        meta: { 
          title: '结算配置',
          permissions: ['settlement:config'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '结算系统', path: '/settlement' },
            { title: '结算配置', path: '/settlement/config' }
          ]
        }
      },
      {
        path: 'upload',
        name: 'SettlementUpload',
        component: () => import('@/views/settlement/upload.vue'),
        meta: { 
          title: '数据上传',
          permissions: ['settlement:upload'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '结算系统', path: '/settlement' },
            { title: '数据上传', path: '/settlement/upload' }
          ]
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    name: 'System',
    meta: { 
      title: '系统管理', 
      icon: 'Setting',
      permissions: ['system:view']
    },
    children: [
      {
        path: 'admin',
        name: 'AdminUser',
        component: () => import('@/views/system/admin.vue'),
        meta: { 
          title: '管理员',
          permissions: ['system:admin'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '系统管理', path: '/system' },
            { title: '管理员', path: '/system/admin' }
          ]
        }
      },
      {
        path: 'role',
        name: 'Role',
        component: () => import('@/views/system/role.vue'),
        meta: { 
          title: '角色管理',
          permissions: ['system:role'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '系统管理', path: '/system' },
            { title: '角色管理', path: '/system/role' }
          ]
        }
      },
      {
        path: 'log',
        name: 'Log',
        component: () => import('@/views/system/log.vue'),
        meta: { 
          title: '操作日志',
          permissions: ['system:log'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '系统管理', path: '/system' },
            { title: '操作日志', path: '/system/log' }
          ]
        }
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/config.vue'),
        meta: { 
          title: '系统配置',
          permissions: ['system:config'],
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '系统管理', path: '/system' },
            { title: '系统配置', path: '/system/config' }
          ]
        }
      },
      {
        path: 'profile',
        name: 'UserProfile',
        component: () => import('@/views/system/profile.vue'),
        meta: { 
          title: '个人信息',
          hidden: true,
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '个人信息', path: '/system/profile' }
          ]
        }
      },
      {
        path: 'password',
        name: 'ChangePassword',
        component: () => import('@/views/system/password.vue'),
        meta: { 
          title: '修改密码',
          hidden: true,
          breadcrumb: [
            { title: '首页', path: '/dashboard' },
            { title: '修改密码', path: '/system/password' }
          ]
        }
      }
    ]
  },
  // 错误页面
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/error/403.vue'),
    meta: { title: '无权限访问', hidden: true }
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面不存在', hidden: true }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404',
    meta: { hidden: true }
  }
]

const createNewRouter = () => createRouter({
  history: createWebHistory(import.meta.env.PROD ? '/admin/' : '/'),
  routes,
  scrollBehavior: () => ({ y: 0 })
})

const router = createNewRouter()

// 重置路由
export function resetRouter() {
  const newRouter = createNewRouter()
  router.matcher = newRouter.matcher
}

export default router
