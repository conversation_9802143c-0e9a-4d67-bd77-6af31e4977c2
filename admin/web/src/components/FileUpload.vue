<template>
  <div class="file-upload-component">
    <el-upload
      ref="uploadRef"
      class="upload-dragger"
      drag
      :action="action"
      :headers="headers"
      :data="data"
      :on-change="handleFileChange"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload"
      :auto-upload="autoUpload"
      :accept="accept"
      :limit="limit"
      :multiple="multiple"
      :disabled="disabled"
    >
      <el-icon class="el-icon--upload">
        <upload-filled />
      </el-icon>
      <div class="el-upload__text">
        {{ uploadText }}
      </div>
      <template #tip>
        <div class="el-upload__tip">
          {{ tipText }}
        </div>
      </template>
    </el-upload>

    <!-- 上传进度 -->
    <div v-if="showProgress && uploadProgress > 0" class="upload-progress">
      <el-progress
        :percentage="uploadProgress"
        :status="progressStatus"
        :stroke-width="6"
      />
      <div class="progress-text">{{ progressText }}</div>
    </div>

    <!-- 文件列表 -->
    <div v-if="showFileList && fileList.length > 0" class="file-list">
      <div class="file-list-header">
        <span>已选择文件 ({{ fileList.length }})</span>
        <el-button @click="clearFiles" type="text" size="small">清空</el-button>
      </div>
      <div class="file-item" v-for="(file, index) in fileList" :key="index">
        <div class="file-info">
          <el-icon><document /></el-icon>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size">({{ formatFileSize(file.size) }})</span>
        </div>
        <div class="file-actions">
          <el-button @click="removeFile(index)" type="text" size="small">
            <el-icon><delete /></el-icon>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { UploadFilled, Document, Delete } from '@element-plus/icons-vue'

export default {
  name: 'FileUpload',
  components: {
    UploadFilled,
    Document,
    Delete
  },
  props: {
    // 上传地址
    action: {
      type: String,
      default: ''
    },
    // 请求头
    headers: {
      type: Object,
      default: () => ({})
    },
    // 额外数据
    data: {
      type: Object,
      default: () => ({})
    },
    // 接受的文件类型
    accept: {
      type: String,
      default: '.xlsx,.xls,.csv'
    },
    // 文件数量限制
    limit: {
      type: Number,
      default: 1
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否自动上传
    autoUpload: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 上传提示文本
    uploadText: {
      type: String,
      default: '将文件拖到此处，或点击上传'
    },
    // 提示文本
    tipText: {
      type: String,
      default: '支持 .xlsx, .xls, .csv 格式文件，文件大小不超过10MB'
    },
    // 是否显示进度条
    showProgress: {
      type: Boolean,
      default: true
    },
    // 是否显示文件列表
    showFileList: {
      type: Boolean,
      default: true
    },
    // 文件大小限制(MB)
    maxSize: {
      type: Number,
      default: 10
    },
    // 允许的文件类型
    allowedTypes: {
      type: Array,
      default: () => [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
      ]
    }
  },
  emits: ['file-change', 'upload-success', 'upload-error', 'before-upload'],
  data() {
    return {
      fileList: [],
      uploadProgress: 0,
      progressStatus: '',
      progressText: ''
    }
  },
  methods: {
    // 文件变化处理
    handleFileChange(file, fileList) {
      this.fileList = fileList
      this.$emit('file-change', file, fileList)
    },
    
    // 上传成功
    handleSuccess(response, file, fileList) {
      this.uploadProgress = 100
      this.progressStatus = 'success'
      this.progressText = '上传成功'
      this.$emit('upload-success', response, file, fileList)
    },
    
    // 上传失败
    handleError(error, file, fileList) {
      this.progressStatus = 'exception'
      this.progressText = '上传失败'
      this.$emit('upload-error', error, file, fileList)
    },
    
    // 上传前检查
    beforeUpload(file) {
      // 检查文件类型
      if (!this.allowedTypes.includes(file.type)) {
        this.$message.error('不支持的文件类型!')
        return false
      }
      
      // 检查文件大小
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize
      if (!isLtMaxSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize}MB!`)
        return false
      }
      
      // 触发自定义验证
      const result = this.$emit('before-upload', file)
      if (result === false) {
        return false
      }
      
      // 设置上传进度
      this.uploadProgress = 0
      this.progressStatus = ''
      this.progressText = '正在上传...'
      
      return true
    },
    
    // 清空文件
    clearFiles() {
      this.fileList = []
      this.uploadProgress = 0
      this.progressStatus = ''
      this.progressText = ''
      this.$refs.uploadRef.clearFiles()
    },
    
    // 移除文件
    removeFile(index) {
      this.fileList.splice(index, 1)
      // 同步更新上传组件的文件列表
      const uploadFiles = this.$refs.uploadRef.uploadFiles
      uploadFiles.splice(index, 1)
    },
    
    // 格式化文件大小
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(1) + ' KB'
      } else {
        return (size / (1024 * 1024)).toFixed(1) + ' MB'
      }
    },
    
    // 手动上传
    submit() {
      this.$refs.uploadRef.submit()
    },
    
    // 取消上传
    abort() {
      this.$refs.uploadRef.abort()
    }
  }
}
</script>

<style scoped>
.file-upload-component {
  width: 100%;
}

.upload-dragger {
  width: 100%;
}

.upload-progress {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

.file-list {
  margin-top: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  font-size: 14px;
  font-weight: 500;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-info .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.file-name {
  font-size: 14px;
  color: #303133;
  margin-right: 8px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  align-items: center;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 7px;
}
</style>