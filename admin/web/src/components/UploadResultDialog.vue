<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="upload-result">
      <!-- 成功状态 -->
      <div v-if="status === 'success'" class="result-success">
        <el-icon class="result-icon success-icon"><circle-check /></el-icon>
        <h3>上传成功</h3>
        <div class="result-summary">
          <p>成功上传 <strong>{{ successCount }}</strong> 条记录</p>
          <p v-if="totalAmount">总金额: <strong>¥{{ totalAmount.toFixed(2) }}</strong></p>
        </div>
      </div>

      <!-- 失败状态 -->
      <div v-if="status === 'error'" class="result-error">
        <el-icon class="result-icon error-icon"><circle-close /></el-icon>
        <h3>上传失败</h3>
        <div class="result-summary">
          <p class="error-message">{{ errorMessage }}</p>
        </div>
      </div>

      <!-- 部分成功状态 -->
      <div v-if="status === 'partial'" class="result-partial">
        <el-icon class="result-icon warning-icon"><warning-filled /></el-icon>
        <h3>部分成功</h3>
        <div class="result-summary">
          <p>成功: <strong>{{ successCount }}</strong> 条，失败: <strong>{{ errorCount }}</strong> 条</p>
        </div>
      </div>

      <!-- 详细信息 -->
      <div v-if="details && details.length > 0" class="result-details">
        <el-divider content-position="left">详细信息</el-divider>
        <div class="details-list">
          <div v-for="(detail, index) in details" :key="index" class="detail-item">
            <el-icon :class="getDetailIconClass(detail.type)">
              <component :is="getDetailIcon(detail.type)" />
            </el-icon>
            <span class="detail-text">{{ detail.message }}</span>
          </div>
        </div>
      </div>

      <!-- 操作建议 -->
      <div v-if="suggestions && suggestions.length > 0" class="result-suggestions">
        <el-divider content-position="left">操作建议</el-divider>
        <ul class="suggestions-list">
          <li v-for="(suggestion, index) in suggestions" :key="index">
            {{ suggestion }}
          </li>
        </ul>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="showRetry" @click="handleRetry" type="primary">重试</el-button>
        <el-button v-if="showViewHistory" @click="handleViewHistory" type="info">查看历史</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { CircleCheck, CircleClose, WarningFilled, InfoFilled, Warning } from '@element-plus/icons-vue'

export default {
  name: 'UploadResultDialog',
  components: {
    CircleCheck,
    CircleClose,
    WarningFilled,
    InfoFilled,
    Warning
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '上传结果'
    },
    status: {
      type: String,
      default: 'success', // success, error, partial
      validator: (value) => ['success', 'error', 'partial'].includes(value)
    },
    successCount: {
      type: Number,
      default: 0
    },
    errorCount: {
      type: Number,
      default: 0
    },
    totalAmount: {
      type: Number,
      default: 0
    },
    errorMessage: {
      type: String,
      default: ''
    },
    details: {
      type: Array,
      default: () => []
    },
    suggestions: {
      type: Array,
      default: () => []
    },
    showRetry: {
      type: Boolean,
      default: false
    },
    showViewHistory: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:modelValue', 'close', 'retry', 'view-history'],
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  methods: {
    handleClose() {
      this.visible = false
      this.$emit('close')
    },
    
    handleRetry() {
      this.$emit('retry')
      this.handleClose()
    },
    
    handleViewHistory() {
      this.$emit('view-history')
      this.handleClose()
    },
    
    getDetailIcon(type) {
      const iconMap = {
        'success': 'CircleCheck',
        'error': 'CircleClose',
        'warning': 'Warning',
        'info': 'InfoFilled'
      }
      return iconMap[type] || 'InfoFilled'
    },
    
    getDetailIconClass(type) {
      const classMap = {
        'success': 'success-icon',
        'error': 'error-icon',
        'warning': 'warning-icon',
        'info': 'info-icon'
      }
      return `detail-icon ${classMap[type] || 'info-icon'}`
    }
  }
}
</script>

<style scoped>
.upload-result {
  text-align: center;
  padding: 20px;
}

.result-success,
.result-error,
.result-partial {
  margin-bottom: 20px;
}

.result-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.warning-icon {
  color: #e6a23c;
}

.result-success h3 {
  color: #67c23a;
  margin: 0 0 16px 0;
  font-size: 20px;
}

.result-error h3 {
  color: #f56c6c;
  margin: 0 0 16px 0;
  font-size: 20px;
}

.result-partial h3 {
  color: #e6a23c;
  margin: 0 0 16px 0;
  font-size: 20px;
}

.result-summary {
  font-size: 16px;
  color: #606266;
  line-height: 1.6;
}

.result-summary p {
  margin: 8px 0;
}

.error-message {
  color: #f56c6c;
  font-weight: 500;
}

.result-details {
  text-align: left;
  margin-top: 20px;
}

.details-list {
  max-height: 200px;
  overflow-y: auto;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-icon {
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.detail-icon.success-icon {
  color: #67c23a;
}

.detail-icon.error-icon {
  color: #f56c6c;
}

.detail-icon.warning-icon {
  color: #e6a23c;
}

.detail-icon.info-icon {
  color: #409eff;
}

.detail-text {
  flex: 1;
  line-height: 1.4;
  color: #606266;
}

.result-suggestions {
  text-align: left;
  margin-top: 20px;
}

.suggestions-list {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  line-height: 1.6;
}

.suggestions-list li {
  margin: 8px 0;
}

.dialog-footer {
  text-align: right;
}

.el-divider {
  margin: 20px 0 15px 0;
}
</style>