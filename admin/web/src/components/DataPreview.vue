<template>
  <div class="data-preview">
    <div class="preview-header">
      <div class="header-left">
        <h4>{{ title }}</h4>
        <div class="data-summary">
          <el-tag type="info" size="small">共 {{ totalRecords }} 条记录</el-tag>
          <el-tag v-if="errorCount > 0" type="danger" size="small">{{ errorCount }} 个错误</el-tag>
          <el-tag v-if="warningCount > 0" type="warning" size="small">{{ warningCount }} 个警告</el-tag>
        </div>
      </div>
      <div class="header-actions">
        <slot name="actions"></slot>
      </div>
    </div>

    <!-- 数据摘要 -->
    <div v-if="showSummary && summary" class="data-summary-section">
      <el-row :gutter="20">
        <el-col v-for="(item, key) in summary" :key="key" :span="6">
          <div class="summary-item">
            <div class="summary-label">{{ getSummaryLabel(key) }}</div>
            <div class="summary-value">{{ formatSummaryValue(key, item) }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <div class="preview-table">
      <el-table
        :data="displayData"
        style="width: 100%"
        :max-height="maxHeight"
        border
        stripe
        :row-class-name="getRowClassName"
      >
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :formatter="column.formatter"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
        >
          <template v-if="column.slot" #default="scope">
            <slot :name="column.slot" :row="scope.row" :column="column" :$index="scope.$index">
              {{ scope.row[column.prop] }}
            </slot>
          </template>
        </el-table-column>
        
        <!-- 状态列 -->
        <el-table-column label="状态" width="100" fixed="right">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row._status)" size="small">
              {{ getStatusText(scope.row._status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页或更多提示 -->
    <div v-if="totalRecords > pageSize" class="preview-footer">
      <div v-if="showPagination" class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalRecords"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
      <div v-else class="more-hint">
        还有 {{ totalRecords - pageSize }} 条记录未显示...
        <el-button @click="showAll" type="text" size="small">显示全部</el-button>
      </div>
    </div>

    <!-- 验证错误详情 -->
    <div v-if="errors.length > 0" class="validation-errors">
      <el-collapse v-model="errorCollapseActive">
        <el-collapse-item title="验证错误详情" name="errors">
          <div class="error-list">
            <div v-for="(error, index) in errors" :key="index" class="error-item">
              <el-icon class="error-icon"><warning /></el-icon>
              <span class="error-text">
                <span v-if="error.row" class="error-row">第{{ error.row }}行:</span>
                {{ error.message }}
              </span>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 验证警告详情 -->
    <div v-if="warnings.length > 0" class="validation-warnings">
      <el-collapse v-model="warningCollapseActive">
        <el-collapse-item title="验证警告详情" name="warnings">
          <div class="warning-list">
            <div v-for="(warning, index) in warnings" :key="index" class="warning-item">
              <el-icon class="warning-icon"><info-filled /></el-icon>
              <span class="warning-text">
                <span v-if="warning.row" class="warning-row">第{{ warning.row }}行:</span>
                {{ warning.message }}
              </span>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import { Warning, InfoFilled } from '@element-plus/icons-vue'

export default {
  name: 'DataPreview',
  components: {
    Warning,
    InfoFilled
  },
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 列配置
    columns: {
      type: Array,
      required: true
    },
    // 标题
    title: {
      type: String,
      default: '数据预览'
    },
    // 最大高度
    maxHeight: {
      type: [String, Number],
      default: 400
    },
    // 每页显示数量
    pageSize: {
      type: Number,
      default: 10
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: false
    },
    // 是否显示摘要
    showSummary: {
      type: Boolean,
      default: true
    },
    // 数据摘要
    summary: {
      type: Object,
      default: null
    },
    // 验证错误
    errors: {
      type: Array,
      default: () => []
    },
    // 验证警告
    warnings: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentPage: 1,
      errorCollapseActive: [],
      warningCollapseActive: []
    }
  },
  computed: {
    totalRecords() {
      return this.data.length
    },
    errorCount() {
      return this.errors.length
    },
    warningCount() {
      return this.warnings.length
    },
    displayData() {
      if (this.showPagination) {
        const start = (this.currentPage - 1) * this.pageSize
        const end = start + this.pageSize
        return this.data.slice(start, end)
      } else {
        return this.data.slice(0, this.pageSize)
      }
    }
  },
  methods: {
    // 获取行样式类名
    getRowClassName({ row, rowIndex }) {
      const actualIndex = this.showPagination 
        ? (this.currentPage - 1) * this.pageSize + rowIndex 
        : rowIndex
      
      const rowErrors = this.errors.filter(error => error.row === actualIndex + 2)
      const rowWarnings = this.warnings.filter(warning => warning.row === actualIndex + 2)
      
      if (rowErrors.length > 0) {
        return 'error-row'
      } else if (rowWarnings.length > 0) {
        return 'warning-row'
      }
      return ''
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'valid': 'success',
        'error': 'danger',
        'warning': 'warning',
        'pending': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'valid': '有效',
        'error': '错误',
        'warning': '警告',
        'pending': '待验证'
      }
      return statusMap[status] || '未知'
    },
    
    // 获取摘要标签
    getSummaryLabel(key) {
      const labelMap = {
        totalRecords: '总记录数',
        totalBaseCommission: '总基础佣金',
        totalTaxAmount: '总纳税额',
        totalActualAmount: '总实际到账',
        totalOrderCount: '总订单数',
        totalPaymentAmount: '总打款金额',
        averageCommission: '平均佣金',
        averagePaymentAmount: '平均打款金额',
        averageOrderCount: '平均订单数'
      }
      return labelMap[key] || key
    },
    
    // 格式化摘要值
    formatSummaryValue(key, value) {
      if (typeof value === 'number') {
        if (key.includes('Amount') || key.includes('Commission')) {
          return '¥' + value.toFixed(2)
        } else if (key.includes('average') && !key.includes('Count')) {
          return '¥' + value.toFixed(2)
        } else if (key.includes('Count')) {
          return Math.round(value)
        }
        return value.toFixed(2)
      }
      return value
    },
    
    // 分页变化
    handlePageChange(page) {
      this.currentPage = page
    },
    
    // 显示全部
    showAll() {
      this.$emit('show-all')
    }
  }
}
</script>

<style scoped>
.data-preview {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #fff;
}

.header-left h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.data-summary {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.data-summary-section {
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.summary-item {
  text-align: center;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.summary-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.preview-table {
  padding: 20px;
  background-color: #fff;
}

.preview-footer {
  padding: 15px 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fff;
}

.more-hint {
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.pagination-wrapper {
  text-align: right;
}

.validation-errors,
.validation-warnings {
  margin: 15px 20px 20px;
}

.error-list,
.warning-list {
  max-height: 200px;
  overflow-y: auto;
}

.error-item,
.warning-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.error-item:last-child,
.warning-item:last-child {
  border-bottom: none;
}

.error-icon {
  color: #f56c6c;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.warning-icon {
  color: #e6a23c;
  margin-right: 8px;
  margin-top: 2px;
  flex-shrink: 0;
}

.error-text,
.warning-text {
  flex: 1;
  line-height: 1.4;
}

.error-row,
.warning-row {
  font-weight: 600;
  margin-right: 4px;
}

/* 表格行样式 */
:deep(.error-row) {
  background-color: #fef0f0 !important;
}

:deep(.warning-row) {
  background-color: #fdf6ec !important;
}

:deep(.error-row:hover) {
  background-color: #fde2e2 !important;
}

:deep(.warning-row:hover) {
  background-color: #faecd8 !important;
}
</style>