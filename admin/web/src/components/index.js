/**
 * 公共组件导出文件
 * 
 * 人工处理系统公共组件集合
 * 提供统一的组件导入接口
 */

// 导入组件
import TaskCard from './TaskCard.vue'
import AttendantSelector from './AttendantSelector.vue'
import RefundForm from './RefundForm.vue'
import Pagination from './Pagination.vue'
import FileUpload from './FileUpload.vue'
import DataPreview from './DataPreview.vue'
import UploadResultDialog from './UploadResultDialog.vue'

// 组件列表
const components = [
  TaskCard,
  AttendantSelector,
  RefundForm,
  Pagination,
  FileUpload,
  DataPreview,
  UploadResultDialog
]

// 默认导出所有组件
export {
  TaskCard,
  AttendantSelector,
  RefundForm,
  Pagination,
  FileUpload,
  DataPreview,
  UploadResultDialog
}

// 批量安装函数
export const install = (app) => {
  components.forEach(component => {
    app.component(component.name || component.__name, component)
  })
}

// 默认导出安装函数
export default install 