import request from '@/utils/request'

// 获取结算配置
export function getSettlementConfig() {
  return request({
    url: '/api/admin/settlement/config',
    method: 'get'
  })
}

// 更新结算配置
export function updateSettlementConfig(data) {
  return request({
    url: '/api/admin/settlement/config',
    method: 'post',
    data
  })
}

// 获取结算配置变更历史
export function getSettlementConfigHistory(params) {
  return request({
    url: '/api/admin/settlement/config/history',
    method: 'get',
    params
  })
}

// 获取分账记录列表
export function getSettlementList(params) {
  return request({
    url: '/api/admin/settlement/list',
    method: 'get',
    params
  })
}

// 触发批量结算
export function triggerBatchSettlement(data) {
  return request({
    url: '/api/admin/settlement/batch',
    method: 'post',
    data
  })
}

// 重新计算分账
export function recalculateSettlement(data) {
  return request({
    url: '/api/admin/settlement/recalculate',
    method: 'post',
    data
  })
}

// 获取分账详情
export function getSettlementDetail(id) {
  return request({
    url: `/api/admin/settlement/${id}`,
    method: 'get'
  })
}

// 上传结算数据
export function uploadSettlementData(data) {
  return request({
    url: '/api/admin/settlement/upload',
    method: 'post',
    data
  })
}

// 上传打款记录
export function uploadPaymentRecords(data) {
  return request({
    url: '/api/admin/payment/upload',
    method: 'post',
    data
  })
}

// 获取上传历史记录
export function getUploadHistory(params) {
  return request({
    url: '/api/admin/settlement/upload/history',
    method: 'get',
    params
  })
}

// 验证上传数据格式
export function validateUploadData(data) {
  return request({
    url: '/api/admin/settlement/upload/validate',
    method: 'post',
    data
  })
}
