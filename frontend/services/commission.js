const request = require('../utils/request')

// 获取佣金明细列表
function getCommissionDetails(params = {}) {
  const query = {
    month: params.month || '', // YYYY-MM 格式
    page: params.page || 1,
    page_size: params.page_size || 20,
    status: params.status || 'all' // all/pending/calculated/paid
  }
  
  return request.get('/attendant/commission/details', { params: query })
    .then(res => {
      console.log('[Service] 获取佣金明细成功:', res)
      return res
    })
    .catch(err => {
      console.error('[Service] 获取佣金明细失败:', err)
      throw err
    })
}

// 获取佣金统计汇总
function getCommissionSummary(params = {}) {
  const query = {
    month: params.month || '', // YYYY-MM 格式
    year: params.year || new Date().getFullYear()
  }
  
  return request.get('/attendant/commission/summary', { params: query })
    .then(res => {
      console.log('[Service] 获取佣金统计成功:', res)
      return res
    })
    .catch(err => {
      console.error('[Service] 获取佣金统计失败:', err)
      throw err
    })
}

// 获取可用的结算月份列表
function getAvailableMonths() {
  return request.get('/attendant/commission/months')
    .then(res => {
      console.log('[Service] 获取可用月份成功:', res)
      return res
    })
    .catch(err => {
      console.error('[Service] 获取可用月份失败:', err)
      throw err
    })
}

// 获取功能开关状态
function getFeatureStatus() {
  return request.get('/attendant/commission/feature-status')
    .then(res => {
      console.log('[Service] 获取功能状态成功:', res)
      return res
    })
    .catch(err => {
      console.error('[Service] 获取功能状态失败:', err)
      throw err
    })
}

module.exports = {
  getCommissionDetails,
  getCommissionSummary,
  getAvailableMonths,
  getFeatureStatus
}