<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-info" bindtap="editUserInfo">
    <user-avatar 
      src="{{userInfo.avatar_url}}"
      username="{{userInfo.nickname}}" 
      size="70px">
    </user-avatar>
    <view class="info">
      <text class="nickname">{{userInfo.nickname || '用户名'}}</text>
      <text class="phone">{{userInfo.phone || '手机号'}}</text>
      <text class="role-tag" wx:if="{{isAttendant}}">陪诊师</text>
    </view>
    <view class="edit-btn">
      <text class="iconfont icon-arrow-right"></text>
    </view>
  </view>

  <view class="menu-list">
    <!-- 陪诊师专属功能区域 -->
    <view class="menu-group" wx:if="{{isAttendant}}">
      <view class="menu-title">
        <text class="title-text">陪诊师工作中心</text>
        <text class="title-desc">高效管理您的陪诊服务</text>
      </view>
      
      <!-- 工作台 - 今日工作快速入口 -->
      <view class="menu-item primary" bindtap="goToAttendantWorkbench" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon primary">🏠</text>
          <view class="menu-content">
            <text class="menu-label">今日工作台</text>
            <text class="menu-desc">查看今日订单，快速处理紧急事务</text>
            <text class="menu-feature" wx:if="{{pendingOrderCount > 0}}">有 {{pendingOrderCount}} 个待处理订单</text>
            <text class="menu-feature" wx:else>今日工作概览与数据统计</text>
          </view>
        </view>
        <view class="menu-right">
          <text class="urgent-badge" wx:if="{{pendingOrderCount > 0}}">{{pendingOrderCount}}</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      
      <!-- 订单管理 - 完整订单管理中心 -->
      <view class="menu-item" bindtap="goToAttendantOrders" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">📋</text>
          <view class="menu-content">
            <text class="menu-label">订单管理中心</text>
            <text class="menu-desc">全面管理历史订单，搜索筛选排序</text>
            <text class="menu-feature">支持高级搜索和数据分析</text>
          </view>
        </view>
        <view class="menu-right">
          <text class="feature-tag">全功能</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      
      <!-- 佣金明细 -->
      <view class="menu-item" bindtap="goToAttendantIncome" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">💰</text>
          <view class="menu-content">
            <text class="menu-label">佣金明细</text>
            <text class="menu-desc">查看佣金明细、结算状态</text>
            <text class="menu-feature">按月自动结算，实时查看收入</text>
          </view>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      
      <!-- 服务管理 -->
      <view class="menu-item" bindtap="goToAttendantServices" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">⚕️</text>
          <view class="menu-content">
            <text class="menu-label">服务设置</text>
            <text class="menu-desc">管理服务类型和工作时间安排</text>
            <text class="menu-feature">智能排班和服务范围配置</text>
          </view>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>

    <!-- 订单相关功能区域 -->
    <view class="menu-group">
      <view class="menu-title">{{isAttendant ? '用户端功能' : '服务管理'}}</view>
      <view class="menu-item" bindtap="goToMedicalServices" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">📑</text>
          <view class="menu-content">
            <text class="menu-label">{{isAttendant ? '我的就医订单' : '我的订单'}}</text>
            <text class="menu-desc" wx:if="{{isAttendant}}">作为普通用户的就医记录</text>
          </view>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item" bindtap="goToPatients" hover-class="menu-item-hover" wx:if="{{!isAttendant}}">
        <view class="menu-left">
          <text class="menu-icon">👥</text>
          <text class="menu-label">就医人管理</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item" bindtap="goToAddress" hover-class="menu-item-hover" wx:if="{{!isAttendant}}">
        <view class="menu-left">
          <text class="menu-icon">📍</text>
          <text class="menu-label">地址管理</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item" bindtap="goToMessages" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">💬</text>
          <text class="menu-label">消息中心</text>
        </view>
        <view class="menu-right">
          <text class="badge" wx:if="{{unreadMessageCount > 0}}">{{unreadMessageCount}}</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
    </view>

    <!-- 设置和支持区域 -->
    <view class="menu-group">
      <view class="menu-title">设置与帮助</view>
      <view class="menu-item" bindtap="goToSecurity" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">🔒</text>
          <text class="menu-label">安全设置</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item" bindtap="goToHelp" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">❓</text>
          <text class="menu-label">帮助中心</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item" bindtap="contactService" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">📞</text>
          <text class="menu-label">联系客服</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item" bindtap="goToFeedback" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">💭</text>
          <text class="menu-label">意见反馈</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>

    <!-- 其他功能区域 -->
    <view class="menu-group">
      <!-- 环境调试入口 - 仅开发环境显示 -->
      <view class="menu-item debug" bindtap="goToEnvironmentDebug" hover-class="menu-item-hover" wx:if="{{isDevelopment}}">
        <view class="menu-left">
          <text class="menu-icon debug">🔧</text>
          <view class="menu-content">
            <text class="menu-label">环境调试</text>
            <text class="menu-desc">开发工具 - 查看和切换运行环境</text>
            <text class="menu-feature">当前: {{currentEnv}}</text>
          </view>
        </view>
        <view class="menu-right">
          <text class="feature-tag debug">DEV</text>
          <text class="iconfont icon-arrow-right"></text>
        </view>
      </view>
      
      <view class="menu-item" bindtap="clearCache" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">🗑️</text>
          <text class="menu-label">清除缓存</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
      <view class="menu-item" bindtap="goToAbout" hover-class="menu-item-hover">
        <view class="menu-left">
          <text class="menu-icon">ℹ️</text>
          <text class="menu-label">关于我们</text>
        </view>
        <text class="iconfont icon-arrow-right"></text>
      </view>
    </view>


  </view>
</view>