const {getUserInfo, clearUserStorage, setUserInfo} = require('../../utils/storage');
const {getUserProfile} = require('../../services/user');
const {checkLogin, getToken} = require('../../utils/storage');
const {isAttendant} = require('../../utils/auth');
const {getAvatarUrl, registerAvatarErrorHandler, DEFAULT_AVATAR} = require('../../utils/avatar-helper');
const {onPageShow, onPageHide, onPageReady, onPageUnload, forceShowTabBar} = require('../../utils/tabbar-manager');
const {getCurrentEnvironment, getEnvironmentInfo} = require('../../config/env');
const {ENV_TYPES} = require('../../config/domains');
const {getMatchingOrderCount} = require('../../services/attendant');
const {getUnreadCount} = require('../../services/message');

Page({
  data: {
    userInfo: null,
    loading: true,
    isAttendant: false,
    pendingOrderCount: 0,    // 待处理订单数量
    unreadMessageCount: 0,   // 未读消息数量
    showGuide: false,        // 是否显示新手引导
    showUsageTip: false,     // 是否显示使用提示
    isFirstTime: false,      // 是否首次使用
    // 环境相关
    isDevelopment: false,    // 是否为开发环境
    currentEnv: ''           // 当前环境描述
  },

  onLoad() {
    console.log('[Settings] Page onLoad')
    registerAvatarErrorHandler(this)
    this.initUserInfo()
    this.initEnvironmentInfo()
    console.log('设置页面加载完成')
    forceShowTabBar()
    
    // 检查是否需要显示引导
    this.checkGuideStatus()
  },

  onShow() {
    console.log('[Settings] Page onShow')
    onPageShow('pages/settings/index')
    forceShowTabBar()
    
    this.refreshUserInfo()
    this.setData({ isAttendant: isAttendant() })
    console.log('设置页面显示，准备刷新用户资料')
    this.fetchUserProfile()
    
    // 获取待处理订单数量和未读消息数量
    if (this.data.isAttendant) {
      this.getPendingOrderCount()
    }
    this.getUnreadMessageCount()

    setTimeout(() => forceShowTabBar(), 200)
  },

  // 刷新用户信息，确保头像URL正确
  refreshUserInfo() {
    const userInfo = getUserInfo()
    if (userInfo) {
      // 确保头像URL设置正确
      if (!userInfo.avatar_url) {
        userInfo.avatar_url = userInfo.avatar || DEFAULT_AVATAR
      }
      
      this.setData({ 
        userInfo: {
          ...userInfo,
          avatar_url: getAvatarUrl(userInfo)
        } 
      })
      console.log('[Settings] 更新用户头像URL:', getAvatarUrl(userInfo))
    }
  },

  onReady() {
    console.log('[Settings] Page onReady')
    onPageReady('pages/settings/index')
    forceShowTabBar()
  },

  onHide() {
    console.log('[Settings] Page onHide')
    onPageHide()
  },

  onUnload() {
    console.log('[Settings] Page onUnload')
    onPageUnload()
  },

  // 处理头像加载错误
  handleAvatarError(e) {
    console.log('[Settings] 头像加载错误，使用默认头像')
    this.setData({
      'userInfo.avatar_url': DEFAULT_AVATAR
    })
  },

  // 初始化用户信息
  initUserInfo() {
    try {
      const userInfo = getUserInfo() || null
      if (userInfo) {
        // 确保头像URL设置正确
        userInfo.avatar_url = getAvatarUrl(userInfo)
      }
      
      const isAttendant = userInfo && userInfo.role === 'attendant'
      console.log('用户信息:', userInfo, '是否陪诊师:', isAttendant, '角色:', userInfo && userInfo.role)
      
      this.setData({ 
        userInfo: userInfo,
        isAttendant: isAttendant
      })
    } catch (error) {
      console.error('加载本地用户信息出错:', error)
    }
  },

  // 从服务器获取最新用户资料
  async fetchUserProfile() {
    try {
      this.setData({ loading: true })
      
      const userInfo = await getUserProfile()
      console.log('获取用户信息结果:', userInfo)
      
      if (userInfo && userInfo.code === 0 && userInfo.data) {
        const userData = userInfo.data
        
        // 确保头像URL设置正确
        userData.avatar_url = getAvatarUrl(userData)
        
        const isAttendant = userData.role === 'attendant'
        console.log('用户角色:', userData.role, '是否陪诊师:', isAttendant)
        
        // 保存到本地存储
        setUserInfo(userData)
        
        this.setData({
          userInfo: userData,
          isAttendant: isAttendant,
          loading: false
        })
      } else {
        console.error('获取用户信息失败:', userInfo)
        this.setData({ loading: false })
      }
    } catch (error) {
      console.error('获取用户信息异常:', error)
      this.setData({ loading: false })
    }
  },

  // 获取待处理订单数量
  async getPendingOrderCount() {
    try {
      console.log('[Settings] 开始获取待处理订单数量...')
      const result = await getMatchingOrderCount()
      console.log('[Settings] 获取待处理订单数量结果:', result)
      
      // 处理API响应数据
      let count = 0
      if (result) {
        // 尝试从不同字段获取数量
        count = result.pending_count || 
                (result.data && result.data.pending_count) || 
                (result.data && result.data.matching_count) || 
                0
      }
      
      console.log('[Settings] 设置待处理订单数量:', count)
      this.setData({ pendingOrderCount: count })
    } catch (error) {
      console.error('[Settings] 获取待处理订单数量失败:', error)
      // 发生错误时设置为0而不是硬编码数字
      this.setData({ pendingOrderCount: 0 })
    }
  },

  // 获取未读消息数量
  async getUnreadMessageCount() {
    try {
      console.log('[Settings] 开始获取未读消息数量...')
      const result = await getUnreadCount()
      console.log('[Settings] 获取未读消息数量结果:', result)
      
      // 处理API响应数据
      let count = 0
      if (result) {
        // 尝试从不同字段获取数量
        count = result.count || 
                (result.data && result.data.count) || 
                (result.data && result.data.unread_count) || 
                result.unread_count ||
                0
      }
      
      console.log('[Settings] 设置未读消息数量:', count)
      this.setData({ unreadMessageCount: count })
    } catch (error) {
      console.error('[Settings] 获取未读消息数量失败:', error)
      // 发生错误时设置为0而不是硬编码数字
      this.setData({ unreadMessageCount: 0 })
    }
  },

  // 编辑用户资料
  editUserInfo() {
    wx.navigateTo({
      url: '/pages/settings/userInfo/index'
    })
  },

  // 初始化环境信息
  initEnvironmentInfo() {
    try {
      const envInfo = getEnvironmentInfo()
      const isDevelopment = envInfo.current === ENV_TYPES.DEV
      
      this.setData({
        isDevelopment,
        currentEnv: envInfo.description
      })
      
      console.log('[Settings] 环境信息初始化:', {
        current: envInfo.current,
        description: envInfo.description,
        isDevelopment
      })
    } catch (error) {
      console.error('初始化环境信息失败:', error)
    }
  },

  // 检查引导状态
  checkGuideStatus() {
    try {
      const guideShown = wx.getStorageSync('attendant_guide_shown')
      const tipShown = wx.getStorageSync('attendant_tip_shown')
      const userInfo = getUserInfo()
      
      if (userInfo && userInfo.role === 'attendant') {
        this.setData({
          showGuide: !guideShown,
          showUsageTip: !tipShown,
          isFirstTime: !guideShown
        })
      }
    } catch (error) {
      console.error('检查引导状态失败:', error)
    }
  },

  // 关闭新手引导
  closeGuide() {
    this.setData({ showGuide: false })
    try {
      wx.setStorageSync('attendant_guide_shown', true)
    } catch (error) {
      console.error('保存引导状态失败:', error)
    }
  },

  // 开始引导体验
  startGuide() {
    this.closeGuide()
    wx.showToast({
      title: '开始体验陪诊师功能',
      icon: 'success',
      duration: 1500
    })
    
    // 延迟跳转到工作台
    setTimeout(() => {
      this.goToAttendantWorkbench()
    }, 1500)
  },

  // 关闭使用提示
  closeUsageTip() {
    this.setData({ showUsageTip: false })
    try {
      wx.setStorageSync('attendant_tip_shown', true)
    } catch (error) {
      console.error('保存提示状态失败:', error)
    }
  },

  // 陪诊师工作台 - 优化后的跳转逻辑
  goToAttendantWorkbench() {
    console.log('点击陪诊师工作台')
    
    // 埋点统计
    wx.reportEvent('attendant_workbench_click', {
      from: 'settings',
      pending_count: this.data.pendingOrderCount,
      is_first_time: this.data.isFirstTime
    })
    
    wx.showLoading({ title: '正在加载工作台...', mask: true })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.navigateTo({
        url: '/pages/attendant/workbench/index',
        success: () => {
          console.log('导航到陪诊师工作台成功')
          // 如果是首次使用，显示工作台引导
          if (this.data.isFirstTime) {
            setTimeout(() => {
              wx.showToast({
                title: '这里是您的每日工作中心',
                icon: 'none',
                duration: 2000
              })
            }, 800)
          }
        },
        fail: (err) => {
          console.error('导航到陪诊师工作台失败:', err)
          wx.showModal({
            title: '提示',
            content: '工作台暂时无法访问，请稍后重试',
            showCancel: false,
            confirmText: '我知道了'
          })
        }
      })
    }, 300)
  },

  // 陪诊师订单管理 - 优化后的跳转逻辑
  goToAttendantOrders() {
    console.log('点击陪诊师订单管理')
    
    // 埋点统计
    wx.reportEvent('attendant_orders_click', {
      from: 'settings'
    })
    
    wx.showLoading({ title: '正在加载订单管理...', mask: true })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.navigateTo({
        url: '/pages/attendant/orders/index',
        success: () => {
          console.log('导航到订单管理成功')
          // 首次使用显示功能说明
          if (this.data.isFirstTime) {
            setTimeout(() => {
              wx.showToast({
                title: '支持搜索筛选的订单管理中心',
                icon: 'none',
                duration: 2000
              })
            }, 800)
          }
        },
        fail: (err) => {
          console.error('导航到订单管理失败:', err)
          wx.showModal({
            title: '提示',
            content: '订单管理页面暂时无法访问，请稍后重试',
            showCancel: false,
            confirmText: '我知道了'
          })
        }
      })
    }, 300)
  },

  // 陪诊师收入管理 - 优化后的跳转逻辑
  goToAttendantIncome() {
    console.log('点击陪诊师收入管理')
    
    // 埋点统计
    wx.reportEvent('attendant_income_click', {
      from: 'settings'
    })
    
    wx.showLoading({ title: '正在加载收入管理...', mask: true })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.navigateTo({
        url: '/pages/attendant/commission/index',
        success: () => console.log('导航到收入管理成功'),
        fail: (err) => {
          console.error('导航到收入管理失败:', err)
          wx.showModal({
            title: '提示',
            content: '收入管理页面暂时无法访问，请稍后重试',
            showCancel: false,
            confirmText: '我知道了'
          })
        }
      })
    }, 300)
  },

  // 陪诊师服务管理 - 优化后的跳转逻辑
  goToAttendantServices() {
    console.log('点击陪诊师服务管理')
    
    // 埋点统计
    wx.reportEvent('attendant_services_click', {
      from: 'settings'
    })
    
    wx.showLoading({ title: '正在加载服务设置...', mask: true })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.navigateTo({
        url: '/pages/attendant/services/index',
        success: () => console.log('导航到服务设置成功'),
        fail: (err) => {
          console.error('导航到服务设置失败:', err)
          wx.showModal({
            title: '提示',
            content: '服务设置页面暂时无法访问，请稍后重试',
            showCancel: false,
            confirmText: '我知道了'
          })
        }
      })
    }, 300)
  },

  // 订单管理 - 优化后的跳转逻辑
  goToMedicalServices() {
    console.log('点击订单管理')
    
    // 根据用户角色显示不同提示
    const title = this.data.isAttendant ? '正在加载就医记录...' : '正在加载我的订单...'
    wx.showLoading({ title, mask: true })
    
    setTimeout(() => {
      wx.hideLoading()
      wx.navigateTo({
        url: '/pages/settings/medical/index',
        success: () => console.log('导航到订单页面成功'),
        fail: (err) => {
          console.error('导航到订单页面失败:', err)
          wx.showModal({
            title: '提示',
            content: '订单页面暂时无法访问，请稍后重试',
            showCancel: false,
            confirmText: '我知道了'
          })
        }
      })
    }, 300)
  },

  // 就医人管理
  goToPatients() {
    wx.navigateTo({
      url: '/pages/settings/patients/index'
    })
  },

  // 地址管理
  goToAddress() {
    wx.navigateTo({
      url: '/pages/settings/address/index'
    })
  },

  // 消息中心
  goToMessages() {
    console.log('点击消息管理')
    wx.navigateTo({
      url: '/pages/settings/messages/index',
      fail: (err) => {
        console.error('导航到消息管理页面失败:', err)
        wx.showToast({ title: '页面跳转失败', icon: 'none' })
      }
    })
  },

  // 安全设置
  goToSecurity() {
    wx.navigateTo({
      url: '/pages/settings/security/index'
    })
  },

  // 帮助中心
  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/index'
    })
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '请拨打客服电话：13001916031\n或添加微信：kefu123',
      showCancel: true,
      cancelText: '取消',
      confirmText: '拨打电话',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: '13001916031',
            fail: () => {
              wx.showToast({
                title: '拨打电话失败',
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },

  // 意见反馈
  goToFeedback() {
    wx.navigateTo({
      url: '/pages/help/feedback/index'
    })
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '提示',
      content: '确定要清除缓存吗？',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorage({
            success: () => {
              wx.showToast({
                title: '清除成功',
                icon: 'success'
              })
            },
            fail: (err) => {
              console.error('清除缓存失败:', err)
              wx.showToast({
                title: '清除缓存失败',
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },

  // 关于我们
  goToAbout() {
    wx.navigateTo({
      url: '/pages/settings/about/index'
    })
  },

  // 跳转到环境调试页面
  goToEnvironmentDebug() {
    console.log('点击环境调试')
    wx.navigateTo({
      url: '/pages/settings/debug-env/index',
      success: () => console.log('导航到环境调试页面成功'),
      fail: (err) => {
        console.error('导航到环境调试页面失败:', err)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },


})