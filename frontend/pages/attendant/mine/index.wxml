<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="info">
        <text class="name">{{userInfo.name}}</text>
        <view class="stats">
          <text class="rating">{{userInfo.rating}}分</text>
          <text class="order-count">已服务{{userInfo.orderCount}}人</text>
        </view>
      </view>
    </view>
    <button class="edit-btn" bindtap="goToEditProfile">编辑资料</button>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-list">
    <view class="menu-item" bindtap="goToWorkbench">
      <view class="menu-content">
        <image class="icon" src="/assets/icons/workbench.png"></image>
        <text class="text">工作台</text>
        <view class="badge" wx:if="{{pendingOrderCount > 0}}">{{pendingOrderCount}}</view>
      </view>
      <image class="arrow" src="/assets/icons/arrow-right.png"></image>
    </view>

    <view class="menu-item" bindtap="goToOrders">
      <view class="menu-content">
        <image class="icon" src="/assets/icons/order.png"></image>
        <text class="text">我的订单</text>
      </view>
      <image class="arrow" src="/assets/icons/arrow-right.png"></image>
    </view>

    <view class="menu-item" bindtap="goToIncome">
      <view class="menu-content">
        <image class="icon" src="/assets/icons/wallet.png"></image>
        <text class="text">佣金明细</text>
      </view>
      <image class="arrow" src="/assets/icons/arrow-right.png"></image>
    </view>

    <view class="menu-item" bindtap="goToSecurity">
      <view class="menu-content">
        <image class="icon" src="/assets/icons/security.png"></image>
        <text class="text">安全设置</text>
      </view>
      <image class="arrow" src="/assets/icons/arrow-right.png"></image>
    </view>
    

    <view class="menu-item" bindtap="goToHelp">
      <view class="menu-content">
        <image class="icon" src="/assets/icons/help.png"></image>
        <text class="text">帮助中心</text>
      </view>
      <image class="arrow" src="/assets/icons/arrow-right.png"></image>
    </view>

    <view class="menu-item" bindtap="contactService">
      <view class="menu-content">
        <image class="icon" src="/assets/icons/service.png"></image>
        <text class="text">联系客服</text>
      </view>
      <image class="arrow" src="/assets/icons/arrow-right.png"></image>
    </view>
  </view>
</view> 