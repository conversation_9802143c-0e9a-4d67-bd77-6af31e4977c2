// 导入必要的API和工具
const {getAttendantProfile, getMatchingOrderCount} = require('../../../services/attendant');
const {CONFIG} = require('../../../config/index');

Page({
  data: {
    userInfo: {
      avatar: '',
      name: '陪诊师',
      rating: 4.8,
      orderCount: 0
    },
    pendingOrderCount: 0,
    pollingTimer: null
  },

  onLoad() {
    // 加载用户信息
    this.loadUserInfo();
    
    // 检查待处理订单数量
    this.checkPendingOrders();
    
    // 启动轮询
    this.startPolling();
  },
  
  onHide() {
    // 页面隐藏时停止轮询
    this.stopPolling();
  },
  
  onUnload() {
    // 页面卸载时停止轮询
    this.stopPolling();
  },
  
  onShow() {
    // 页面显示时检查订单并启动轮询
    this.checkPendingOrders();
    this.startPolling();
  },
  
  // 启动轮询
  startPolling() {
    // 清除之前的轮询
    this.stopPolling();
    
    // 设置30秒自动刷新
    this.data.pollingTimer = setInterval(() => {
      this.checkPendingOrders();
    }, 30000); // 30秒
  },
  
  // 停止轮询
  stopPolling() {
    if (this.data.pollingTimer) {
      clearInterval(this.data.pollingTimer);
      this.data.pollingTimer = null;
    }
  },
  
  // 检查待处理订单
  checkPendingOrders() {
    getMatchingOrderCount()
      .then(res => {
        const countData = res.data || res;
        this.setData({
          pendingOrderCount: countData.pending_count || 0
        });
      })
      .catch(err => {
        console.error('获取待处理订单数量失败:', err);
      });
  },
  
  // 加载用户信息
  loadUserInfo() {
    getAttendantProfile()
      .then(res => {
        const profileData = res.data || res;
        this.setData({
          userInfo: {
            name: profileData.name || '陪诊师',
            avatar: profileData.avatar || '',
            rating: profileData.rating || 4.8,
            orderCount: profileData.order_count || 0
          }
        });
      })
      .catch(err => {
        console.error('获取陪诊师信息失败:', err);
      });
  },
  
  // 跳转到工作台
  goToWorkbench() {
    wx.reLaunch({
      url: '/pages/attendant/workbench/index',
      fail: (err) => {
        console.error('跳转到工作台失败:', err);
        // 失败时尝试使用switchTab
        wx.showToast({
          title: '加载中...',
          icon: 'loading',
          duration: 2000
        });
        
        // 打印详细错误信息供调试
        console.log('工作台跳转详细错误:', JSON.stringify(err));
        
        // 延迟后再次尝试
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/attendant/workbench/index',
            fail: (err2) => {
              console.error('二次跳转仍然失败:', err2);
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }, 500);
      }
    });
  },

  // 跳转到订单列表
  goToOrders() {
    wx.navigateTo({
      url: '/pages/attendant/orders/index'
    });
  },

  // 跳转到收入明细（佣金明细）
  goToIncome() {
    wx.navigateTo({
      url: '/pages/attendant/commission/index',
      success: () => {
        console.log('成功跳转到佣金明细页面');
      },
      fail: (err) => {
        console.error('跳转佣金明细页面失败:', err);
        // 如果跳转失败，尝试跳转到原收入页面
        wx.navigateTo({
          url: '/pages/attendant/income/index',
          fail: (err2) => {
            console.error('跳转收入页面也失败:', err2);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'error'
            });
          }
        });
      }
    });
  },

  // 跳转到安全设置
  goToSecurity() {
    wx.navigateTo({
      url: '/pages/attendant/security/index'
    });
  },

  // 跳转到帮助中心
  goToHelp() {
    wx.navigateTo({
      url: '/pages/help/index'
    });
  },

  // 跳转到个人资料编辑
  goToEditProfile() {
    wx.navigateTo({
      url: '/pages/attendant/profile/edit/index'
    });
  },

  // 联系客服
  contactService() {
    wx.makePhoneCall({
      phoneNumber: CONFIG.CUSTOMER_SERVICE.PHONE
    })
  }
}) 