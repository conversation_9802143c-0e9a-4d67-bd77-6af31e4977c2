const commissionService = require('../../../services/commission');
const { formatMoney } = require('../../../utils/util');
const { formatDateTime } = require('../../../utils/datetime');

Page({
  data: {
    loading: false,
    isRefreshing: false,
    
    // 月份选择
    currentMonth: '',
    availableMonths: [],
    showMonthPicker: false,
    
    // 佣金数据
    commissionList: [],
    summary: {
      totalCommission: '0.00',
      totalTax: '0.00',
      totalActual: '0.00',
      totalOrders: 0
    },
    
    // 分页
    page: 1,
    hasMore: true,
    
    // 筛选
    statusFilter: 'all', // all/pending/calculated/paid
    statusOptions: [
      { value: 'all', label: '全部' },
      { value: 'pending', label: '待结算' },
      { value: 'calculated', label: '已结算' },
      { value: 'paid', label: '已打款' }
    ],
    
    // 功能状态
    withdrawalEnabled: false,
    showWithdrawalTip: false
  },

  onLoad(options) {
    // 处理从其他页面传递的参数
    if (options.from) {
      console.log('从页面跳转而来:', options.from);
    }
    this.initPage();
  },

  onShow() {
    // 从其他页面返回时刷新数据
    this.loadCommissionData();
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true });
      
      // 设置默认月份为当前月份
      const now = new Date();
      const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      this.setData({ currentMonth });
      
      // 并行加载初始数据
      await Promise.all([
        this.loadAvailableMonths(),
        this.checkFeatureStatus(),
        this.loadCommissionData()
      ]);
      
    } catch (error) {
      console.error('初始化页面失败:', error);
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载可用月份
  async loadAvailableMonths() {
    try {
      const res = await commissionService.getAvailableMonths();
      if (res.code === 0) {
        this.setData({
          availableMonths: res.data || []
        });
      }
    } catch (error) {
      console.error('加载可用月份失败:', error);
    }
  },

  // 检查功能状态
  async checkFeatureStatus() {
    try {
      const res = await commissionService.getFeatureStatus();
      if (res.code === 0) {
        this.setData({
          withdrawalEnabled: res.data.withdrawal_enabled || false,
          showWithdrawalTip: !res.data.withdrawal_enabled
        });
      }
    } catch (error) {
      console.error('检查功能状态失败:', error);
    }
  },

  // 加载佣金数据
  async loadCommissionData(reset = true) {
    try {
      const { currentMonth, statusFilter, page, commissionList } = this.data;
      const currentPage = reset ? 1 : page;

      // 并行加载明细和汇总
      const [detailsRes, summaryRes] = await Promise.all([
        commissionService.getCommissionDetails({
          month: currentMonth,
          page: currentPage,
          page_size: 20,
          status: statusFilter === 'all' ? '' : statusFilter
        }),
        commissionService.getCommissionSummary({
          month: currentMonth
        })
      ]);

      // 处理明细数据
      if (detailsRes.code === 0) {
        const newList = (detailsRes.data.records || []).map(item => ({
          id: item.id,
          settlementMonth: item.settlement_month,
          baseCommission: formatMoney(item.base_commission || 0),
          taxAmount: formatMoney(item.tax_amount || 0),
          actualAmount: formatMoney(item.actual_amount || 0),
          settlementStatus: item.settlement_status,
          settlementStatusText: this.getStatusText(item.settlement_status),
          orderCount: item.order_count || 0,
          paymentTime: item.payment_time,
          createdAt: formatDateTime(item.created_at),
          updatedAt: formatDateTime(item.updated_at)
        }));

        this.setData({
          commissionList: reset ? newList : [...commissionList, ...newList],
          page: currentPage + 1,
          hasMore: newList.length >= 20
        });
      }

      // 处理汇总数据
      if (summaryRes.code === 0) {
        const summary = summaryRes.data.summary || {};
        this.setData({
          summary: {
            totalCommission: formatMoney(summary.total_commission || 0),
            totalTax: formatMoney(summary.total_tax || 0),
            totalActual: formatMoney(summary.total_actual || 0),
            totalOrders: summary.total_orders || 0
          }
        });
      }

    } catch (error) {
      console.error('加载佣金数据失败:', error);
      throw error;
    }
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待结算',
      'calculated': '已结算',
      'paid': '已打款'
    };
    return statusMap[status] || '未知状态';
  },

  // 月份选择
  onMonthPickerShow() {
    this.setData({ showMonthPicker: true });
  },

  onMonthPickerHide() {
    this.setData({ showMonthPicker: false });
  },

  onMonthChange(e) {
    const selectedMonth = e.currentTarget.dataset.month;
    if (selectedMonth === this.data.currentMonth) {
      this.onMonthPickerHide();
      return;
    }

    this.setData({
      currentMonth: selectedMonth,
      showMonthPicker: false,
      page: 1,
      hasMore: true
    });

    this.loadCommissionData(true);
  },

  // 状态筛选
  onStatusFilterChange(e) {
    const status = e.currentTarget.dataset.status;
    if (status === this.data.statusFilter) return;

    this.setData({
      statusFilter: status,
      page: 1,
      hasMore: true
    });

    this.loadCommissionData(true);
  },

  // 下拉刷新
  async onRefresh() {
    this.setData({ isRefreshing: true });
    try {
      await Promise.all([
        this.loadAvailableMonths(),
        this.checkFeatureStatus(),
        this.loadCommissionData(true)
      ]);
    } finally {
      this.setData({ isRefreshing: false });
    }
  },

  // 上拉加载更多
  async onLoadMore() {
    if (!this.data.hasMore || this.data.loading) return;
    
    try {
      await this.loadCommissionData(false);
    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 查看详情
  onCommissionDetail(e) {
    const item = e.currentTarget.dataset.item;
    // 可以跳转到详情页面或显示更多信息
    wx.showModal({
      title: '佣金详情',
      content: `结算月份：${item.settlementMonth}\n基础佣金：¥${item.baseCommission}\n纳税额度：¥${item.taxAmount}\n实际到账：¥${item.actualAmount}\n订单数量：${item.orderCount}单\n结算状态：${item.settlementStatusText}`,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 跳转到原取现页面（如果功能开启）
  goToWithdraw() {
    if (!this.data.withdrawalEnabled) {
      wx.showModal({
        title: '功能暂停',
        content: '取现功能暂时停用，系统已升级为按月自动结算模式。您的佣金将在每月固定时间自动结算并打款到您的账户。',
        confirmText: '了解详情',
        cancelText: '知道了',
        success: (res) => {
          if (res.confirm) {
            // 跳转到功能暂停说明页面
            wx.navigateTo({
              url: '/pages/attendant/feature-suspended/index?title=' + encodeURIComponent('取现功能升级') + '&message=' + encodeURIComponent('系统已升级为按月自动结算模式')
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/attendant/income/withdraw/index'
    });
  },

  // 查看功能升级说明
  showFeatureUpgradeInfo() {
    wx.navigateTo({
      url: '/pages/attendant/feature-suspended/index?title=' + encodeURIComponent('功能升级说明') + '&message=' + encodeURIComponent('了解新的佣金结算模式')
    });
  },

  // 联系客服
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '如有结算相关问题，请联系客服咨询。',
      confirmText: '联系客服',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到客服页面或拨打客服电话
          wx.showToast({
            title: '客服功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '陪诊师佣金明细',
      path: '/pages/index/index'
    };
  }
});