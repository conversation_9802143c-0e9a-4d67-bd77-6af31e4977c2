# 陪诊师佣金明细页面

## 概述

陪诊师佣金明细页面是新的佣金结算系统的核心前端组件，替代了原有的取现管理功能。该页面提供按月查看佣金明细、结算状态跟踪和收入统计等功能。

## 功能特性

### 1. 月份选择
- 支持选择不同月份查看佣金明细
- 动态加载可用的结算月份
- 默认显示当前月份数据

### 2. 佣金统计
- 显示月度佣金汇总信息
- 包含基础佣金、纳税额度、实际到账金额
- 显示订单数量统计

### 3. 明细列表
- 按月显示详细的佣金记录
- 支持按结算状态筛选（全部/待结算/已结算/已打款）
- 显示每条记录的详细信息

### 4. 状态管理
- 实时显示结算状态
- 支持下拉刷新和上拉加载更多
- 处理空状态和加载状态

### 5. 功能切换
- 检测取现功能开关状态
- 在功能暂停时显示相应提示
- 支持联系客服功能

## 文件结构

```
frontend/pages/attendant/commission/
├── index.js      # 页面逻辑
├── index.wxml    # 页面模板
├── index.wxss    # 页面样式
├── index.json    # 页面配置
└── README.md     # 文档说明
```

## API 接口

### 获取佣金明细
- **接口**: `GET /attendant/commission/details`
- **参数**: month, page, page_size, status
- **返回**: 佣金记录列表和分页信息

### 获取佣金统计
- **接口**: `GET /attendant/commission/summary`
- **参数**: month, year
- **返回**: 月度佣金汇总数据

### 获取可用月份
- **接口**: `GET /attendant/commission/months`
- **返回**: 可查询的月份列表

### 获取功能状态
- **接口**: `GET /attendant/commission/feature-status`
- **返回**: 取现功能和自动结算功能的开关状态

## 数据格式

### 佣金记录
```javascript
{
  id: 1,
  settlement_month: "2025-01",
  base_commission: 100.00,
  tax_amount: 6.00,
  actual_amount: 94.00,
  settlement_status: "paid", // pending/calculated/paid
  order_count: 5,
  payment_time: "2025-01-15T10:00:00Z",
  created_at: "2025-01-01T00:00:00Z"
}
```

### 佣金汇总
```javascript
{
  total_commission: 500.00,
  total_tax: 30.00,
  total_actual: 470.00,
  total_orders: 25
}
```

## 状态说明

- **pending**: 待结算 - 订单已完成，等待月度结算
- **calculated**: 已结算 - 财务已完成线下计算，等待打款
- **paid**: 已打款 - 佣金已成功打款到账户

## 使用说明

1. 页面加载时自动显示当前月份的佣金数据
2. 点击月份选择器可切换查看不同月份
3. 使用状态筛选器可过滤不同结算状态的记录
4. 点击佣金记录可查看详细信息
5. 支持下拉刷新和上拉加载更多功能

## 测试

运行测试命令：
```bash
cd frontend
npm test -- --testPathPattern=commission.test.js
```

## 注意事项

1. 该页面需要陪诊师身份认证
2. 数据按月分页加载，提高性能
3. 支持功能开关控制，可动态切换新旧模式
4. 所有金额显示使用格式化函数确保一致性
5. 错误处理覆盖网络异常和数据异常情况