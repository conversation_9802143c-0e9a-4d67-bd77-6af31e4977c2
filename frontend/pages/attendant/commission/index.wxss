.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 佣金概览 */
.commission-overview {
  background: linear-gradient(180deg, #07c160 0%, #06ad56 100%);
  padding: 40rpx 30rpx;
  color: #fff;
  position: relative;
  margin-bottom: 20rpx;
}

/* 月份选择器 */
.month-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  margin-bottom: 30rpx;
  backdrop-filter: blur(10rpx);
}

.month-text {
  font-size: 32rpx;
  font-weight: 600;
  margin-right: 12rpx;
}

.arrow-icon {
  font-size: 24rpx;
  opacity: 0.8;
  transition: transform 0.3s ease;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 20rpx;
}

.main-stat {
  text-align: center;
  margin-bottom: 40rpx;
}

.main-stat .label {
  font-size: 28rpx;
  opacity: 0.9;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.main-stat .amount {
  display: block;
  font-size: 72rpx;
  font-weight: 700;
  margin-top: 16rpx;
  letter-spacing: 2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.stat-item {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  letter-spacing: 1rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 功能暂停提示 */
.withdrawal-tip {
  margin: 20rpx 0;
  padding: 24rpx;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1rpx solid #ffd700;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
}

.tip-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #856404;
}

.tip-content {
  font-size: 26rpx;
  color: #856404;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.tip-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12rpx;
}

.info-btn {
  background: transparent;
  color: #1565c0;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid #1565c0;
  border-radius: 6rpx;
  margin: 0;
}

.info-btn::after {
  border: none;
}

.contact-btn {
  background: #1565c0;
  color: #fff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  border: none;
  margin: 0;
}

.contact-btn::after {
  border: none;
}

/* 操作按钮区域 */
.action-section {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
}

.action-btn {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 40rpx;
  margin: 0;
  padding: 0 60rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn::after {
  border: none;
}

.withdraw-btn {
  background: #fff;
  color: #07c160;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15);
}

.withdraw-btn:active {
  opacity: 0.9;
  transform: scale(0.95);
}

/* 佣金明细 */
.commission-details {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  margin-top: -20rpx;
  padding: 30rpx;
  min-height: calc(100vh - 400rpx);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 明细区域标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 4rpx;
}

.filter-tab {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #666;
  border-radius: 6rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-tab.active {
  background: #07c160;
  color: #fff;
}

.detail-list {
  height: calc(100vh - 560rpx);
}

.detail-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

.item-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.item-info {
  flex: 1;
  margin-right: 20rpx;
}

.item-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.month {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
}

.status-badge {
  font-size: 22rpx;
  font-weight: 500;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: #fff;
}

.status-pending {
  background: #ff9800;
}

.status-calculated {
  background: #2196f3;
}

.status-paid {
  background: #07c160;
}

.item-details {
  space-y: 8rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.detail-value.tax {
  color: #ff9800;
}

.detail-value.time {
  color: #07c160;
}

.amount-section {
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.amount-label {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.amount {
  font-size: 36rpx;
  font-weight: 700;
  color: #07c160;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 120rpx;
}

.empty image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 20rpx;
}

.empty text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.empty-tip {
  font-size: 24rpx !important;
  color: #ccc !important;
}

/* 加载更多 */
.load-more, .no-more, .loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
}

.loading {
  color: #07c160;
}

/* 月份选择弹窗 */
.month-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.month-picker {
  background: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-height: 800rpx;
  margin: 40rpx;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-close {
  font-size: 36rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.month-list {
  max-height: 600rpx;
}

.month-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f8f9fa;
  font-size: 30rpx;
  color: #333;
  transition: all 0.3s ease;
}

.month-item:last-child {
  border-bottom: none;
}

.month-item:active {
  background: #f8f9fa;
}

.month-item.active {
  background: #f0f9ff;
  color: #07c160;
  font-weight: 600;
}

.check-icon {
  font-size: 32rpx;
  color: #07c160;
}

.empty-text {
  color: #999;
  text-align: center;
}