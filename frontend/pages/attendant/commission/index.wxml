<view class="container">
  <!-- 佣金概览 -->
  <view class="commission-overview">
    <!-- 月份选择器 -->
    <view class="month-selector" bindtap="onMonthPickerShow">
      <text class="month-text">{{currentMonth || '选择月份'}}</text>
      <text class="arrow-icon">▼</text>
    </view>
    
    <!-- 佣金统计 -->
    <view class="stats-section">
      <view class="main-stat">
        <text class="label">本月实际到账（元）</text>
        <text class="amount">{{summary.totalActual}}</text>
      </view>
      
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-value">{{summary.totalCommission}}</text>
          <text class="stat-label">基础佣金</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{summary.totalTax}}</text>
          <text class="stat-label">纳税额度</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{summary.totalOrders}}</text>
          <text class="stat-label">订单数量</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{summary.totalActual}}</text>
          <text class="stat-label">实际到账</text>
        </view>
      </view>
    </view>

    <!-- 功能暂停提示 -->
    <view class="withdrawal-tip" wx:if="{{showWithdrawalTip}}">
      <view class="tip-header">
        <text class="tip-icon">📢</text>
        <text class="tip-title">系统升级通知</text>
      </view>
      <text class="tip-content">取现功能已升级为按月自动结算模式，您的佣金将在每月固定时间自动结算并打款。</text>
      <view class="tip-actions">
        <button class="info-btn" bindtap="showFeatureUpgradeInfo">了解详情</button>
        <button class="contact-btn" bindtap="onContactService">联系客服</button>
      </view>
    </view>

    <!-- 操作按钮（如果取现功能开启） -->
    <view class="action-section" wx:if="{{withdrawalEnabled}}">
      <button class="action-btn withdraw-btn" bindtap="goToWithdraw">
        查看取现
      </button>
    </view>
  </view>

  <!-- 佣金明细 -->
  <view class="commission-details">
    <view class="section-header">
      <text class="section-title">佣金明细</text>
      <!-- 状态筛选器 -->
      <view class="filter-tabs">
        <view class="filter-tab {{statusFilter === item.value ? 'active' : ''}}" 
              wx:for="{{statusOptions}}" wx:key="value"
              data-status="{{item.value}}" bindtap="onStatusFilterChange">
          {{item.label}}
        </view>
      </view>
    </view>
    
    <scroll-view 
      class="detail-list" 
      scroll-y 
      enable-back-to-top 
      refresher-enabled
      bindrefresherrefresh="onRefresh"
      refresher-triggered="{{isRefreshing}}"
      bindscrolltolower="onLoadMore"
      enhanced="{{true}}"
      bounces="{{false}}"
    >
      <view class="detail-item" wx:for="{{commissionList}}" wx:key="id" 
            data-item="{{item}}" bindtap="onCommissionDetail">
        <view class="item-main">
          <view class="item-info">
            <view class="item-title">
              <text class="month">{{item.settlementMonth}} 结算</text>
              <view class="status-badge status-{{item.settlementStatus}}">
                {{item.settlementStatusText}}
              </view>
            </view>
            <view class="item-details">
              <view class="detail-row">
                <text class="detail-label">基础佣金：</text>
                <text class="detail-value">¥{{item.baseCommission}}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">纳税额度：</text>
                <text class="detail-value tax">¥{{item.taxAmount}}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">订单数量：</text>
                <text class="detail-value">{{item.orderCount}}单</text>
              </view>
              <view class="detail-row" wx:if="{{item.paymentTime}}">
                <text class="detail-label">打款时间：</text>
                <text class="detail-value time">{{item.paymentTime}}</text>
              </view>
            </view>
          </view>
          <view class="amount-section">
            <text class="amount-label">实际到账</text>
            <text class="amount">¥{{item.actualAmount}}</text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore && commissionList.length > 0}}">
        <text>加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && commissionList.length > 0}}">
        <text>没有更多了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty" wx:if="{{commissionList.length === 0 && !loading}}">
        <image src="/assets/images/empty.png" mode="aspectFit"></image>
        <text>暂无佣金明细</text>
        <text class="empty-tip">完成订单后将在此显示佣金信息</text>
      </view>

      <!-- 加载状态 -->
      <view class="loading" wx:if="{{loading}}">
        <text>加载中...</text>
      </view>
    </scroll-view>
  </view>

  <!-- 月份选择弹窗 -->
  <view class="month-picker-overlay" wx:if="{{showMonthPicker}}" bindtap="onMonthPickerHide">
    <view class="month-picker" catchtap="">
      <view class="picker-header">
        <text class="picker-title">选择月份</text>
        <text class="picker-close" bindtap="onMonthPickerHide">✕</text>
      </view>
      <scroll-view class="month-list" scroll-y>
        <view class="month-item {{currentMonth === item ? 'active' : ''}}" 
              wx:for="{{availableMonths}}" wx:key="*this"
              data-month="{{item}}" bindtap="onMonthChange">
          <text>{{item}}</text>
          <text class="check-icon" wx:if="{{currentMonth === item}}">✓</text>
        </view>
        <view class="month-item" wx:if="{{availableMonths.length === 0}}">
          <text class="empty-text">暂无可选月份</text>
        </view>
      </scroll-view>
    </view>
  </view>
</view>