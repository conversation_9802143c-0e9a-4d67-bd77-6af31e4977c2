const {ORDER_STATUS, ORDER_STATUS_TEXT, getOrderDisplayText} = require('../../../utils/order-status-constants');
const {getUserInfo} = require('../../../utils/storage');
const {getAttendantOrders, 
  getAttendantMatchingOrderCount, 
  getAttendantInfo,
  acceptOrderMatching,
  rejectOrderMatching,
  completeOrder} = require('../../../services/attendant');
const {formatTime, formatMoney} = require('../../../utils/util');
const {onPageShow, onPageHide, onPageReady, onPageUnload, forceShowTabBar} = require('../../../utils/tabbar-manager');
const eventBus = require('../../../utils/event');
const {EVENT_NAMES} = require('../../../utils/event');
const {notifyFromAttendantOrders} = require('../../../utils/status-notifier');
const {setNotificationPage} = require('../../../services/notification');

Page({
  data: {
    // 使用统一的状态定义
    ORDER_STATUS,
    statusTextMap: ORDER_STATUS_TEXT,
    
    loading: false,
    attendantInfo: null,
    attendantId: null,
    matchingCount: 0,
    unreadCount: 0,
    orders: [],
    currentTab: 'all',
    tabs: [
      { id: 'all', text: '全部', count: 0 },
      { id: 'pending', text: '待处理', count: 0 },
      { id: 'processing', text: '进行中', count: 0 },
      { id: 'completed', text: '已完成', count: 0 }
    ],
    
    // 排序和视图
    viewMode: 'card', // 'card' 或 'list'
    sortIndex: 0,
    sortOptions: [
      { label: '最新优先', value: 'date_desc' },
      { label: '最早优先', value: 'date_asc' },
      { label: '金额高低', value: 'amount_desc' },
      { label: '金额低高', value: 'amount_asc' }
    ],
    
    // 分页和加载
    page: 1,
    pageSize: 10,
    hasMore: true,
    
    // 通知队列
    notifications: [],
    // 上一次的匹配数量，用于检测变化
    lastMatchingCount: null,
    isVerified: true, // 默认为已认证，避免不必要的认证提示
    isActive: true,  // 默认为活跃状态
    empty: false,
    networkError: false,
    isRefreshing: false
  },

  onLoad() {
    this.loadAttendantInfo()
    this.loadOrders()
    this.loadUnreadCount()
    
    // 设置通知页面实例
    setNotificationPage(this)
  },

  onShow() {
    // 每次页面显示时刷新订单列表和计数
    this.loadAttendantInfo()
    this.loadOrders()
    this.loadMatchingCount()
  },

  onReachBottom() {
    // 触底加载更多
    this.loadMoreOrders()
  },

  // 排序切换
  onSortChange(e) {
    const index = parseInt(e.detail.value)
    this.setData({ 
      sortIndex: index,
      page: 1,
      hasMore: true,
      orders: []
    })
    this.loadOrders()
  },

  // 视图模式切换
  switchViewMode(e) {
    const mode = e.currentTarget.dataset.mode
    this.setData({
      viewMode: mode
    })
  },

  // 检查API响应状态
  isSuccessResponse(res) {
    // 检查是否有响应数据
    if (!res) return false;
    
    // 返回格式1：含有code字段的标准响应格式
    if (typeof res === 'object' && 
        (res.code === 0 || res.code === 200 || 
         (res.data && (res.data.code === 0 || res.data.code === 200)))) {
      return true;
    }
    
    // 返回格式2：直接返回数据对象，检查是否包含必要字段判断是否有效
    if (typeof res === 'object') {
      // 陪诊师信息对象 - 检查是否包含id字段
      if (res.attendant && res.attendant.id) return true;
      
      // 直接是陪诊师对象 - 检查是否包含id字段
      if (res.id) return true;
      
      // 订单列表 - 检查是否包含items或orders字段
      if (res.items || res.orders) return true;
      
      // 匹配订单数量 - 检查是否包含count字段
      if (typeof res.count !== 'undefined' || typeof res.matching_count !== 'undefined') return true;
    }
    
    return false;
  },

  // 安全获取API响应数据
  getResponseData(res) {
    if (!res) return {};
    
    // 处理不同的API响应格式
    // 1. {code: 0, data: {...}} - 标准格式，返回data部分
    if (res.data && typeof res.data === 'object') {
      return res.data;
    }
    
    // 2. 直接返回数据对象
    return res;
  },

  // 加载陪诊师信息
  loadAttendantInfo() {
    getAttendantInfo()
      .then(res => {
        console.log('获取陪诊师信息返回:', res)
        
        // 检查是否有效的返回数据
        if (this.isSuccessResponse(res)) {
          // 安全获取响应数据
          const data = this.getResponseData(res);
          
          // 尝试从不同路径获取陪诊师信息
          const attendantInfo = data.attendant || data || {};
          
          // 认证状态判断：只要有有效的陪诊师ID就视为已认证
          const isVerified = !!(attendantInfo && attendantInfo.id && attendantInfo.id > 0);
          
          // 活跃状态判断：status为1表示正常服务状态
          const isActive = !!(attendantInfo && attendantInfo.status === 1);
          
          console.log('陪诊师信息解析:', {
            attendantInfo,
            isVerified,
            isActive,
            id: attendantInfo.id,
            status: attendantInfo.status
          });
          
          this.setData({
            attendantInfo,
            attendantId: attendantInfo.id,
            isVerified,
            isActive
          });
          
          // 如果已认证，加载匹配计数
          if (isVerified) {
            this.loadMatchingCount();
          }
        } else {
          // 未找到陪诊师信息，保持默认状态
          console.log('无效的陪诊师信息返回');
        }

        // 无论是否认证，都尝试加载订单列表
        this.loadOrders(true);
      })
      .catch(err => {
        console.error('获取陪诊师信息失败:', err);
        this.setData({
          networkError: true
        });
        
        // 即使获取陪诊师信息失败，也尝试加载订单列表
        this.loadOrders(true);
      });
  },

  // 加载匹配订单数量
  loadMatchingCount() {
    if (!this.data.attendantId) return;
    
    getAttendantMatchingOrderCount(this.data.attendantId)
      .then(res => {
        console.log('获取匹配订单数量返回:', res);
        
        if (this.isSuccessResponse(res)) {
          // 安全获取响应数据
          const data = this.getResponseData(res);
          const count = data.count || data.matching_count || 0;
          
          // 更新标签计数
          this.setData({
            matchingCount: count,
            'tabs[1].count': count
          });
          
          // 检查是否有新的匹配订单
          if (this.data.lastMatchingCount !== null && count > this.data.lastMatchingCount) {
            // 有新的匹配订单，显示通知
            notifyFromAttendantOrders(this, '您有新的订单需要处理');
          }
          
          // 更新上次计数
          this.setData({
            lastMatchingCount: count
          });
        }
      })
      .catch(err => {
        console.error('获取匹配订单数量失败:', err);
      });
  },

  // 加载订单列表
  loadOrders(reset = true) {
    if (this.data.loading) return Promise.resolve();
    
    // 设置加载状态
    this.setData({
      loading: true,
      networkError: false
    });
    
    // 如果是刷新，重置页码
    if (reset) {
      this.setData({
        page: 1,
        hasMore: true,
        orders: []
      });
    }
    
    // 构建请求参数
    const params = {
      page: this.data.page,
      page_size: this.data.pageSize,
      sort: this.data.sortOptions[this.data.sortIndex].value
    };
    
    // 根据当前标签添加状态筛选
    if (this.data.currentTab === 'pending') {
      params.status = 'pending';
    } else if (this.data.currentTab === 'processing') {
      params.status = 'processing';
    } else if (this.data.currentTab === 'completed') {
      params.status = 'completed';
    }
    
    console.log('开始加载订单列表，参数:', params);
    
    // 发起请求
    const fetchData = () => {
      return getAttendantOrders(params)
        .then(res => {
          // 隐藏加载状态
          this.setData({
            loading: false
          });
          
          console.log('获取订单列表返回:', res);
          
          // 检查响应是否成功
          if (this.isSuccessResponse(res)) {
            // 安全获取响应数据
            const data = this.getResponseData(res);
            const orders = data.orders || data.items || data.list || [];
            
            console.log('原始订单数据:', JSON.stringify(orders));
            
            const newOrders = orders.map(item => this.formatOrderItem(item));
            
            // 打印订单列表信息
            console.log(`格式化后的订单列表 (${newOrders.length}条):`, 
              newOrders.map(order => ({id: order.id, type: order.type, orderNo: order.orderNo})));
            
            // 输出详细的格式化后订单数据，用于诊断问题
            console.log('格式化后的完整订单列表:', JSON.stringify(newOrders));
            
            // 更新订单列表
            this.setData({
              orders: reset ? newOrders : [...this.data.orders, ...newOrders],
              hasMore: newOrders.length >= this.data.pageSize,
              empty: reset && newOrders.length === 0
            });
            
            // 在数据设置完成后检查页面数据
            setTimeout(() => {
              console.log('当前页面数据状态:', this.data.orders);
              // 微信小程序环境下无法直接使用document
              console.log('页面中的订单数量:', this.data.orders.length);
              // 在微信小程序环境中，需要使用wx.createSelectorQuery()查询DOM
              const query = wx.createSelectorQuery();
              query.selectAll('.order-card').boundingClientRect();
              query.exec((res) => {
                console.log('查询到的订单卡片:', res);
              });
            }, 500);
            
            // 更新页码
            if (newOrders.length > 0) {
              this.setData({
                page: this.data.page + 1
              });
            }
            
            // 更新标签计数
            const summary = data.summary || {};
            if (summary) {
              this.updateTabCounts(summary);
            }
            
            return res;
          } else {
            // 获取错误信息
            const errorMsg = res && res.message ? res.message : 
                            (res && res.msg ? res.msg : '加载失败');
            
            wx.showToast({
              title: errorMsg,
              icon: 'none'
            });
            return res;
          }
        })
        .catch(err => {
          console.error('加载订单列表失败:', err);
          this.setData({
            loading: false,
            networkError: true
          });
          return Promise.reject(err);
        });
    };
    
    // 执行请求
    return fetchData();
  },

  // 加载更多订单
  loadMoreOrders() {
    // 触底加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrders(false);
    }
  },

  // 格式化订单数据
  formatOrderItem(item) {
    console.log('格式化订单项 - 原始数据:', JSON.stringify(item));
    
    // 获取患者信息 - 从截图中可以看到订单数据结构中包含order字段
    // 患者信息可能在不同位置: item.patient, item.order.patient或其他
    let patientName = '未知患者';
    let hospital = '未指定医院';
    let department = '未指定科室';
    
    // 尝试从item直接获取患者信息
    if (item.patient && item.patient.name) {
      patientName = item.patient.name;
    } else if (item.patient_name) {
      patientName = item.patient_name;
    }
    
    // 尝试从order字段获取患者信息
    if (item.order) {
      if (item.order.patient && item.order.patient.name) {
        patientName = item.order.patient.name;
      } else if (item.order.patient_name) {
        patientName = item.order.patient_name;
      }
      
      // 获取医院和科室信息
      hospital = item.order.hospital_name || item.hospital_name || '未指定医院';
      department = item.order.department_name || item.department_name || '未指定科室';
    } else {
      hospital = item.hospital_name || '未指定医院';
      department = item.department || '未指定科室';
    }
    
    // 首先尝试获取最可能作为订单ID的字段
    // 优先级: order_id > item.order.id > item.id
    const orderId = item.order_id || (item.order && item.order.id) || item.id;
    
    // 从order中获取订单编号和状态
    const orderNo = item.order ? (item.order.order_no || item.order.id) : (item.order_no || item.id);
    const status = item.order ? item.order.status : item.status;
    const orderType = item.order ? item.order.type : item.type;
    
    // 格式化服务时间
    const serviceTime = this.formatDateTime(
        item.appointment_time ||
  item.service_time ||
  (item.order && item.order.appointment_time) ||
  (item.order && item.order.service_time) || 
      item.create_time || 
      (item.order && item.order.create_time)
    );
    
    // 格式化金额
    const amount = formatMoney(
      item.amount || 
      (item.order && item.order.amount) || 
      0
    );
    
    // 构建格式化后的订单项
    const formattedItem = {
      ...item,
      id: orderId, // 使用提取的orderId
      orderNo: orderNo,
      serviceTime: serviceTime,
      amount: amount,
      patientName: patientName,
      hospital: hospital,
      department: department,
      type: orderType || 'appointment',
      statusText: this.getOrderStatusText(status, item.match_type)
    };
    
    // 添加状态类
    if (status === 2) {
      formattedItem.statusClass = 'status-pending';
    } else if (status === 3) {
      formattedItem.statusClass = 'status-processing';
    // } else if (status === 4) { // 已废弃，统一使用T+2审核流程
    //   formattedItem.statusClass = 'status-completed';
    } else if (status === 5) {
      formattedItem.statusClass = 'status-cancelled';
    } else if (status === 10) { // 审核通过显示为已完成
      formattedItem.statusClass = 'status-completed';
    }
    
    console.log('格式化后的订单项(关键字段):', {
      id: formattedItem.id,
      type: formattedItem.type,
      orderNo: formattedItem.orderNo,
      patientName: formattedItem.patientName
    });
    
    // 确保ID被正确设置
    if (!formattedItem.id) {
      console.error('警告: 订单ID为空！原始数据:', item);
      // 设置一个临时ID，确保UI正常显示
      formattedItem.id = orderNo || Math.random().toString(36).substring(2, 15);
    }
    
    return formattedItem;
  },

  // 更新标签计数
  updateTabCounts(summary) {
    this.setData({
      'tabs[0].count': summary.total || 0,
      'tabs[1].count': summary.pending || 0,
      'tabs[2].count': summary.processing || 0,
      'tabs[3].count': summary.completed || 0
    });
  },

  // 格式化日期时间
  formatDateTime(dateStr) {
    if (!dateStr) return '未设置时间';
    
    try {
      // 统一处理ISO 8601格式的时间字符串
      // 例如: 2025-05-28T00:00:00+08:00 或 2025-06-02T11:40:10+08:00
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return '无效日期';
      
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      
      // 计算日期差异
      const diffTime = targetDate.getTime() - today.getTime();
      const diffDays = diffTime / (1000 * 60 * 60 * 24);
      
      // 格式化时间部分
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const timeStr = `${hours}:${minutes}`;
      
      // 根据日期差异返回不同的格式
      if (diffDays === 0) {
        return `今天 ${timeStr}`;
      } else if (diffDays === 1) {
        return `明天 ${timeStr}`;
      } else if (diffDays === -1) {
        return `昨天 ${timeStr}`;
      } else {
        // 月份从0开始，需要+1
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${month}-${day} ${timeStr}`;
      }
    } catch (error) {
      console.error('日期格式化错误:', error, dateStr);
      return dateStr; // 返回原始字符串，防止显示出错
    }
  },

  // 获取订单状态文本
  getOrderStatusText(status, matchingStatus) {
    return getOrderDisplayText(status, matchingStatus);
  },

  // 获取空状态文本
  getEmptyText() {
    if (this.data.networkError) {
      return '网络连接失败';
    }
    
    if (this.data.currentTab === 'all') {
      return '暂无订单';
    } else if (this.data.currentTab === 'pending') {
      return '暂无待处理订单';
    } else if (this.data.currentTab === 'processing') {
      return '暂无进行中订单';
    } else if (this.data.currentTab === 'completed') {
      return '暂无已完成订单';
    }
    
    return '暂无数据';
  },

  // 获取空状态描述
  getEmptyDesc() {
    if (this.data.networkError) {
      return '请检查网络连接或稍后重试';
    }
    return '接单后的订单将显示在这里';
  },

  // 加载未读消息数
  loadUnreadCount() {
    // 实现加载未读消息数的逻辑
  },

  // 下拉刷新
  onRefresh() {
    this.setData({ isRefreshing: true });
    this.loadOrders(true).finally(() => {
      this.setData({ isRefreshing: false });
    });
  },

  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.currentTab) return;
    
    this.setData({
      currentTab: tab,
      page: 1,
      hasMore: true,
      orders: []
    });
    this.loadOrders();
  },

  // 查看订单详情
  goToOrderDetail(e) {
    const { id, type } = e.currentTarget.dataset;
    console.log('跳转到订单详情页:', id, type);
    
    if (!id) {
      console.error('订单ID不存在，无法跳转');
      wx.showToast({
        title: '订单数据错误',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: `/pages/attendant/orders/detail/index?id=${id}`
    });
  },

  // 直接查看订单详情，阻止事件冒泡
  viewOrderDetail(e) {
    // 阻止冒泡，避免触发外层容器的点击事件
    e.stopPropagation();
    
    // 打印完整的事件对象，用于调试
    console.log('详情按钮点击事件对象:', e);
    console.log('点击的详情按钮 dataset:', e.currentTarget.dataset);
    
    // 尝试从dataset获取ID
    let id = e.currentTarget.dataset.id;
    
    // 如果无法从dataset获取ID，尝试从index获取
    if (!id) {
      // 获取点击按钮所在的列表项索引
      const index = e.currentTarget.dataset.index;
      console.log('尝试从索引获取订单ID, 索引:', index);
      
      // 如果有索引，使用索引从orders数组中获取对应的订单
      if (index !== undefined && this.data.orders && this.data.orders[index]) {
        id = this.data.orders[index].id;
        console.log('通过索引获取到订单ID:', id);
      }
    }
    
    // 如果仍然无法获取ID，尝试获取事件中可能的其他信息
    if (!id && e.target && e.target.dataset) {
      id = e.target.dataset.id;
      console.log('从target.dataset尝试获取ID:', id);
    }
    
    console.log('通过详情按钮跳转到订单详情页，最终ID:', id);
    
    // 检查id是否有效
    if (!id) {
      // 如果还是没有ID，使用当前屏幕上显示的第一个订单ID
      if (this.data.orders && this.data.orders.length > 0) {
        id = this.data.orders[0].id;
        console.warn('无法获取准确的订单ID，使用列表中第一个订单ID:', id);
        
        wx.showToast({
          title: '获取订单信息失败，将显示第一个订单',
          icon: 'none',
          duration: 2000
        });
      } else {
        console.error('订单ID不存在，无法跳转到详情页');
        wx.showToast({
          title: '订单数据错误',
          icon: 'none'
        });
        return;
      }
    }
    
    // 添加一个短暂的延迟，确保日志输出完成
    setTimeout(() => {
      // 直接跳转到详情页，只传递id参数
      wx.navigateTo({
        url: `/pages/attendant/orders/detail/index?id=${id}`
      });
    }, 100);
  },

  // 跳转到个人资料
  goToProfile() {
    wx.navigateTo({
      url: '/pages/attendant/profile/index'
    });
  },

  // 跳转到工作时间管理 - 保留但不在UI中显示入口
  goToSchedule() {
    wx.navigateTo({
      url: '/pages/attendant/schedule/index'
    });
  },

  // 跳转到收入管理 - 保留但不在UI中显示入口
  goToIncome() {
    wx.navigateTo({
      url: '/pages/attendant/commission/index'
    });
  },

  // 跳转到评价管理 - 保留但不在UI中显示入口
  goToReviews() {
    wx.navigateTo({
      url: '/pages/attendant/reviews/index'
    });
  },

  // 跳转到认证
  goToVerify() {
    wx.navigateTo({
      url: '/pages/attendant/verify/index'
    });
  },

  // 接受订单
  acceptOrder(e) {
    const { id, type } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认接单',
      content: '接受该订单后，您需要在约定时间提供陪诊服务',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '处理中...' });
          
          acceptOrderMatching(id, type)
            .then(res => {
              wx.hideLoading();
              if (this.isSuccessResponse(res)) {
                wx.showToast({
                  title: '接单成功',
                  icon: 'success'
                });
                this.loadOrders();
              } else {
                wx.showToast({
                  title: res && res.message ? res.message : '接单失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              wx.hideLoading();
              console.error('接单失败:', err);
              wx.showToast({
                title: '网络异常',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 拒绝订单
  rejectOrder(e) {
    const { id, type } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认拒绝',
      content: '确定要拒绝该订单吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '处理中...' });
          
          rejectOrderMatching(id, type)
            .then(res => {
              wx.hideLoading();
              if (this.isSuccessResponse(res)) {
                wx.showToast({
                  title: '已拒绝订单',
                  icon: 'success'
                });
                this.loadOrders();
              } else {
                wx.showToast({
                  title: res && res.message ? res.message : '拒绝失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              wx.hideLoading();
              console.error('拒绝订单失败:', err);
              wx.showToast({
                title: '网络异常',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 完成订单
  completeOrder(e) {
    const { id, type } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '完成服务',
      content: '确认完成此次服务吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '处理中...' });
          
          completeOrder(id, type)
            .then(res => {
              wx.hideLoading();
              if (this.isSuccessResponse(res)) {
                wx.showToast({
                  title: '已完成服务',
                  icon: 'success'
                });
                this.loadOrders();
              } else {
                wx.showToast({
                  title: res && res.message ? res.message : '完成失败',
                  icon: 'none'
                });
              }
            })
            .catch(err => {
              wx.hideLoading();
              console.error('完成订单失败:', err);
              wx.showToast({
                title: '网络异常',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  // 通知相关方法
  onNotificationClick(e) {
    const { index } = e.detail;
    // 处理通知点击
    console.log('点击通知:', index);
  },

  onNotificationClose(e) {
    const { index } = e.detail;
    // 处理通知关闭
    console.log('关闭通知:', index);
  }
}) 