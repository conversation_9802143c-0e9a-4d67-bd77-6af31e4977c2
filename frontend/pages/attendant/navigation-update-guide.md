# 小程序页面路由和导航更新指南

## 更新概述

本次更新主要针对陪诊师佣金结算系统的页面路由和导航进行了优化，从原有的取现管理模式转变为佣金明细查看模式。

## 主要变更

### 1. 新增页面

#### 功能暂停提示页面
- **路径**: `/pages/attendant/feature-suspended/index`
- **用途**: 当取现功能关闭时，向用户展示功能升级说明
- **特性**: 
  - 支持通过URL参数自定义标题和消息
  - 提供跳转到佣金明细页面的快捷入口
  - 包含联系客服功能

### 2. 页面路由更新

#### 陪诊师个人中心 (`/pages/attendant/mine/index`)
- **变更**: "我的收入" 菜单项更名为 "佣金明细"
- **跳转目标**: 从 `/pages/attendant/income/index` 改为 `/pages/attendant/commission/index`
- **容错处理**: 如果佣金页面跳转失败，会自动回退到原收入页面

#### 收入管理页面 (`/pages/attendant/income/index`)
- **新增功能**: 自动检测取现功能状态
- **重定向逻辑**: 当取现功能关闭时，自动重定向到功能暂停页面
- **参数传递**: 通过URL参数传递自定义的标题和消息

#### 佣金明细页面 (`/pages/attendant/commission/index`)
- **新增功能**: 支持接收来源页面参数
- **升级说明**: 新增"了解详情"按钮，跳转到功能升级说明页面
- **优化体验**: 改进了取现功能关闭时的提示信息

### 3. 导航流程

```mermaid
graph TD
    A[陪诊师个人中心] --> B[点击佣金明细]
    B --> C[佣金明细页面]
    
    D[收入管理页面] --> E{检查取现功能状态}
    E -->|功能关闭| F[功能暂停页面]
    E -->|功能开启| G[显示原收入页面]
    
    F --> H[查看佣金明细]
    H --> C
    
    C --> I[了解详情]
    I --> F
```

### 4. 数据传递机制

#### URL参数传递
- **功能暂停页面**: 支持 `title` 和 `message` 参数自定义显示内容
- **佣金明细页面**: 支持 `from` 参数记录来源页面

#### 示例
```javascript
// 跳转到功能暂停页面并传递自定义内容
wx.redirectTo({
  url: '/pages/attendant/feature-suspended/index?title=' + 
       encodeURIComponent('取现功能升级') + 
       '&message=' + 
       encodeURIComponent('系统已升级为按月自动结算模式')
});

// 跳转到佣金明细页面并记录来源
wx.navigateTo({
  url: '/pages/attendant/commission/index?from=income'
});
```

## 测试验证

### 手动测试步骤

1. **测试个人中心导航**
   - 进入陪诊师个人中心页面
   - 点击"佣金明细"菜单项
   - 验证是否正确跳转到佣金明细页面

2. **测试取现功能重定向**
   - 确保后端取现功能开关为关闭状态
   - 直接访问收入管理页面
   - 验证是否自动重定向到功能暂停页面

3. **测试功能暂停页面**
   - 在功能暂停页面点击"查看佣金明细"
   - 验证是否正确跳转到佣金明细页面
   - 测试"联系客服"和"返回"功能

4. **测试佣金页面升级说明**
   - 在佣金明细页面找到升级提示
   - 点击"了解详情"按钮
   - 验证是否跳转到功能升级说明页面

### 自动化测试

运行测试脚本：
```javascript
// 在小程序开发者工具控制台中运行
require('./test/navigation-flow-test.js').runAllTests();
```

## 注意事项

### 1. 向后兼容性
- 原有的收入管理页面仍然保留，确保在取现功能开启时正常工作
- 所有跳转都包含容错处理，避免页面跳转失败

### 2. 用户体验
- 重定向使用 `wx.redirectTo` 而非 `wx.navigateTo`，避免用户通过返回按钮回到已废弃的页面
- 提供清晰的功能升级说明，帮助用户理解变更

### 3. 数据一致性
- 页面间的数据传递使用URL参数，确保数据的准确性
- 所有参数都进行了适当的编码处理

## 部署清单

- [ ] 确认 `app.json` 中已添加新页面路径
- [ ] 验证所有页面文件已正确创建
- [ ] 测试各种导航场景
- [ ] 确认后端API支持功能状态检查
- [ ] 验证用户权限和数据安全

## 相关需求

- **需求 1.1**: 暂停陪诊师的取现功能
- **需求 3.1**: 陪诊师访问原取现管理页面位置时显示佣金明细查看页面

## 更新日期

2025-01-08