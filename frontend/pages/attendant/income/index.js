const request = require('../../../utils/request');
const {formatMoney} = require('../../../utils/util');
const {formatDateTime} = require('../../../utils/datetime');

Page({
  data: {
    isRefreshing: false,
    loading: false,
    accountInfo: {
      totalIncome: '0.00',
      balance: '0.00',
      frozenBalance: '0.00',
      totalWithdrawal: '0.00'
    },
    todayIncome: '0.00',
    monthIncome: '0.00',
    incomeList: [],
    page: 1,
    hasMore: true,
    filterType: 'all', // all: 全部, income: 收入, withdraw: 提现
    showSettlementTip: false,
    showRedirectTip: false
  },

  onLoad() {
    // 检查是否应该重定向到新的佣金页面
    this.checkRedirectToCommission()
  },

  onShow() {
    // 从其他页面返回时刷新数据
    this.checkRedirectToCommission()
  },

  // 检查是否重定向到佣金页面
  async checkRedirectToCommission() {
    try {
      // 检查功能开关状态
      const res = await request.get('/attendant/commission/feature-status')
      if (res.code === 0 && !res.data.withdrawal_enabled) {
        // 如果取现功能关闭，重定向到功能暂停页面
        wx.redirectTo({
          url: '/pages/attendant/feature-suspended/index?title=' + encodeURIComponent('取现功能升级') + '&message=' + encodeURIComponent('系统已升级为按月自动结算模式'),
          fail: () => {
            // 如果重定向失败，显示提示并加载原页面数据
            this.setData({ showRedirectTip: true })
            this.loadIncomeData()
          }
        })
        return
      }
    } catch (error) {
      console.error('检查功能状态失败:', error)
    }
    
    // 如果取现功能开启或检查失败，加载原页面数据
    this.loadIncomeData()
  },

  // 加载收入数据
  async loadIncomeData() {
    try {
      this.setData({ loading: true })
      
      // 并行获取账户信息和收入列表
      await Promise.all([
        this.getAccountInfo(),
        this.getIncomeList(true) // true表示重新加载
      ])
      
      // 获取今日和本月收入统计
      await this.getIncomeStats()
      
    } catch (error) {
      console.error('加载收入数据失败:', error)
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 获取账户信息
  async getAccountInfo() {
    try {
      const res = await request.get('/attendant/account/info')

      if (res.code === 0) {
        const frozenBalance = res.data.frozen_balance || 0
        const frozenBalanceFloat = parseFloat(frozenBalance.toString().replace(/[^0-9.]/g, ''))
        
        this.setData({
          accountInfo: {
            totalIncome: formatMoney(res.data.total_income || 0),
            balance: formatMoney(res.data.balance || 0),
            frozenBalance: formatMoney(frozenBalance),
            totalWithdrawal: formatMoney(res.data.total_withdrawal || 0)
          },
          showSettlementTip: frozenBalanceFloat > 0
        })
      }
    } catch (error) {
      console.error('获取账户信息失败:', error)
      throw error
    }
  },

  // 获取收入列表
  async getIncomeList(reset = false) {
    try {
      const { page, filterType, incomeList } = this.data
      const currentPage = reset ? 1 : page

      const res = await request.get('/attendant/account/logs', {
        params: {
          page: currentPage,
          page_size: 20,
          type: filterType === 'all' ? '' : (filterType === 'income' ? 1 : 2)
        }
      })

      if (res.code === 0) {
        const newList = (res.data.list || []).map(item => ({
          id: item.id,
          orderNo: item.related_type === 'order' ? `PZ${item.related_id}` : `TX${item.id}`,
          time: formatDateTime(item.created_at),
          amount: formatMoney(Math.abs(item.amount)),
          type: item.type === 1 ? 'income' : 'withdraw',
          desc: item.description || '收入明细',
          status: item.status || 'pending',
          statusText: item.status_text || '处理中',
          relatedId: item.related_id,
          relatedType: item.related_type
        }))

        this.setData({
          incomeList: reset ? newList : [...incomeList, ...newList],
          page: currentPage + 1,
          hasMore: newList.length >= 20
        })
      }
    } catch (error) {
      console.error('获取收入列表失败:', error)
      throw error
    }
  },

  // 获取收入统计
  async getIncomeStats() {
    try {
      const res = await request.get('/attendant/account/stats')

      if (res.code === 0) {
        this.setData({
          todayIncome: formatMoney(res.data.today_income || 0),
          monthIncome: formatMoney(res.data.month_income || 0)
        })
      }
    } catch (error) {
      console.error('获取收入统计失败:', error)
    }
  },

  // 下拉刷新
  async onRefresh() {
    this.setData({ isRefreshing: true })
    try {
      await this.loadIncomeData()
    } finally {
      this.setData({ isRefreshing: false })
    }
  },

  // 上拉加载更多
  async onLoadMore() {
    if (!this.data.hasMore || this.data.loading) return
    
    try {
      await this.getIncomeList(false)
    } catch (error) {
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    }
  },

  // 筛选类型切换
  onFilterChange(e) {
    const filterType = e.currentTarget.dataset.type
    if (filterType === this.data.filterType) return

    this.setData({
      filterType,
      page: 1,
      hasMore: true
    })
    this.getIncomeList(true)
  },

  // 跳转到提现页面
  goToWithdraw() {
    console.log('点击提现按钮，当前账户信息:', this.data.accountInfo)
    
    const balanceStr = this.data.accountInfo.balance || '0.00'
    // 移除逗号和其他格式化字符，只保留数字和小数点
    const balance = parseFloat(balanceStr.toString().replace(/[^0-9.]/g, ''))
    
    console.log('解析后的余额:', balance)
    
    if (isNaN(balance) || balance <= 0) {
      wx.showToast({
        title: '暂无可提现金额',
        icon: 'none'
      })
      return
    }

    console.log('准备跳转到提现页面，余额:', balance)
    
    wx.navigateTo({
      url: `/pages/attendant/income/withdraw/index?balance=${balance}`,
      success: () => {
        console.log('成功跳转到提现页面')
      },
      fail: (err) => {
        console.error('跳转提现页面失败:', err)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },

  // 跳转到提现记录
  goToRecords() {
    console.log('点击提现记录按钮')
    
    wx.navigateTo({
      url: '/pages/attendant/income/records/index',
      success: () => {
        console.log('成功跳转到提现记录页面')
      },
      fail: (err) => {
        console.error('跳转提现记录页面失败:', err)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },

  // 查看收入详情
  onIncomeDetail(e) {
    const item = e.currentTarget.dataset.item
    if (item.relatedType === 'order' && item.relatedId) {
      wx.navigateTo({
        url: `/pages/attendant/orders/detail/index?id=${item.relatedId}`
      })
    }
  },

  // 跳转到佣金明细页面
  goToCommission() {
    wx.navigateTo({
      url: '/pages/attendant/commission/index',
      success: () => {
        console.log('成功跳转到佣金明细页面')
      },
      fail: (err) => {
        console.error('跳转佣金明细页面失败:', err)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '陪诊师收入管理',
      path: '/pages/index/index'
    }
  }
}) 