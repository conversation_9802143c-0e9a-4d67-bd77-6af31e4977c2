<view class="container">
  <!-- 收入概览 -->
  <view class="income-overview">
    <view class="total-section">
      <text class="label">总收入（元）</text>
      <text class="amount">{{accountInfo.totalIncome}}</text>
    </view>
    
    <!-- 收入统计 -->
    <view class="stats-grid">
      <view class="stat-item">
        <text class="stat-value">{{todayIncome}}</text>
        <text class="stat-label">今日收入</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{monthIncome}}</text>
        <text class="stat-label">本月收入</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{accountInfo.balance}}</text>
        <text class="stat-label">可提现余额</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{accountInfo.totalWithdrawal}}</text>
        <text class="stat-label">已提现</text>
      </view>
    </view>

    <!-- 提现说明提示 -->
    <view class="settlement-tip" wx:if="{{showSettlementTip}}">
      <view class="tip-header">
        <text class="tip-icon">⏰</text>
        <text class="tip-title">结算提醒</text>
      </view>
      <text class="tip-content">您有 ¥{{accountInfo.frozenBalance}} 收入正在结算中（7天结算期），结算完成后即可提现</text>
    </view>

    <!-- 系统升级提示 -->
    <view class="redirect-tip" wx:if="{{showRedirectTip}}">
      <view class="tip-header">
        <text class="tip-icon">🔄</text>
        <text class="tip-title">系统升级</text>
      </view>
      <text class="tip-content">收入管理已升级为佣金明细模式，点击下方按钮查看新功能</text>
      <view class="tip-actions">
        <button class="redirect-btn" bindtap="goToCommission">查看佣金明细</button>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn withdraw-btn" bindtap="goToWithdraw">
        立即提现
      </button>
      <button class="action-btn records-btn" bindtap="goToRecords">
        提现记录
      </button>
    </view>
  </view>

  <!-- 收入明细 -->
  <view class="income-details">
    <view class="section-header">
      <text class="section-title">收入明细</text>
      <!-- 筛选器 -->
      <view class="filter-tabs">
        <view class="filter-tab {{filterType === 'all' ? 'active' : ''}}" 
              data-type="all" bindtap="onFilterChange">全部</view>
        <view class="filter-tab {{filterType === 'income' ? 'active' : ''}}" 
              data-type="income" bindtap="onFilterChange">收入</view>
        <view class="filter-tab {{filterType === 'withdraw' ? 'active' : ''}}" 
              data-type="withdraw" bindtap="onFilterChange">提现</view>
      </view>
    </view>
    
    <scroll-view 
      class="detail-list" 
      scroll-y 
      enable-back-to-top 
      refresher-enabled
      bindrefresherrefresh="onRefresh"
      refresher-triggered="{{isRefreshing}}"
      bindscrolltolower="onLoadMore"
      enhanced="{{true}}"
      bounces="{{false}}"
    >
      <view class="detail-item" wx:for="{{incomeList}}" wx:key="id" 
            data-item="{{item}}" bindtap="onIncomeDetail">
        <view class="item-main">
          <view class="item-info">
            <view class="item-title">
              <text class="desc">{{item.desc}}</text>
              <text class="status {{item.status}}">{{item.statusText}}</text>
            </view>
            <view class="item-meta">
              <text class="order-no">{{item.orderNo}}</text>
              <text class="time">{{item.time}}</text>
            </view>
          </view>
          <view class="amount-section">
            <text class="amount {{item.type === 'withdraw' ? 'minus' : 'plus'}}">
              {{item.type === 'withdraw' ? '-' : '+'}}{{item.amount}}
            </text>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{hasMore && incomeList.length > 0}}">
        <text>加载更多...</text>
      </view>

      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && incomeList.length > 0}}">
        <text>没有更多了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty" wx:if="{{incomeList.length === 0 && !loading}}">
        <image src="/assets/images/empty.png" mode="aspectFit"></image>
        <text>暂无收入明细</text>
      </view>

      <!-- 加载状态 -->
      <view class="loading" wx:if="{{loading}}">
        <text>加载中...</text>
      </view>
    </scroll-view>
  </view>
</view> 