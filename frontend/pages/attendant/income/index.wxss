.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 收入概览 */
.income-overview {
  background: linear-gradient(180deg, #07c160 0%, #06ad56 100%);
  padding: 40rpx 30rpx;
  color: #fff;
  position: relative;
  margin-bottom: 20rpx;
}

.total-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.total-section .label {
  font-size: 32rpx;
  opacity: 0.9;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.total-section .amount {
  display: block;
  font-size: 80rpx;
  font-weight: 600;
  margin-top: 20rpx;
  letter-spacing: 2rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 收入统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 40rpx;
}

.stat-item {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.stat-value {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  letter-spacing: 1rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 操作按钮区域 */
.action-section {
  margin-top: 40rpx;
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  font-weight: 600;
  border-radius: 40rpx;
  margin: 0;
  padding: 0;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  max-width: 280rpx;
}

.action-btn::after {
  border: none;
}

.withdraw-btn {
  background: #fff;
  color: #07c160;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15);
}

.withdraw-btn:active {
  opacity: 0.9;
  transform: scale(0.95);
}

.records-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.records-btn:active {
  background: rgba(255, 255, 255, 0.15);
  transform: scale(0.95);
}

/* 收入明细 */
.income-details {
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  margin-top: -20rpx;
  padding: 30rpx;
  min-height: calc(100vh - 400rpx);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 明细区域标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 4rpx;
}

.filter-tab {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  color: #666;
  border-radius: 6rpx;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #07c160;
  color: #fff;
}

.detail-list {
  height: calc(100vh - 560rpx);
}

.detail-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item:active {
  background: #f8f9fa;
  transform: scale(0.98);
}

.item-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.item-info {
  flex: 1;
}

.item-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.desc {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-no {
  font-size: 26rpx;
  color: #666;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.amount-section {
  text-align: right;
}

.amount {
  font-size: 36rpx;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
}

.amount.plus {
  color: #07c160;
}

.amount.minus {
  color: #ff4d4f;
}

.status {
  font-size: 24rpx;
  font-weight: 500;
}

.status.pending {
  color: #ff9800;
}

.status.success {
  color: #07c160;
}

.status.failed {
  color: #ff4d4f;
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 120rpx;
}

.empty image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 20rpx;
}

.empty text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.load-more, .no-more, .loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
}

.loading {
  color: #07c160;
}

/* 结算提醒样式 */
.settlement-tip {
  margin: 20rpx 0;
  padding: 24rpx;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 1rpx solid #ffd700;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.2);
}

/* 重定向提示样式 */
.redirect-tip {
  margin: 20rpx 0;
  padding: 24rpx;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 1rpx solid #2196f3;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.2);
}

.tip-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.tip-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #856404;
}

.tip-content {
  font-size: 26rpx;
  color: #856404;
  line-height: 1.5;
}

.redirect-tip .tip-title {
  color: #1565c0;
}

.redirect-tip .tip-content {
  color: #1565c0;
  margin-bottom: 16rpx;
}

.tip-actions {
  display: flex;
  justify-content: flex-end;
}

.redirect-btn {
  background: #2196f3;
  color: #fff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  border: none;
  margin: 0;
}

.redirect-btn::after {
  border: none;
} 