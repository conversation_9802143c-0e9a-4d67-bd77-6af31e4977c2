Page({
  data: {
    title: '功能升级通知',
    message: '取现功能已升级为按月自动结算模式',
    description: '为了提供更好的服务体验，我们已将取现功能升级为按月自动结算模式。您的佣金将在每月固定时间自动结算并打款到您的账户。',
    features: [
      '每月自动结算，无需手动申请',
      '结算时间固定，流程更透明',
      '详细的佣金明细查看',
      '实时的结算状态跟踪'
    ]
  },

  onLoad(options) {
    // 可以通过参数自定义页面内容
    if (options.title) {
      this.setData({ title: decodeURIComponent(options.title) });
    }
    if (options.message) {
      this.setData({ message: decodeURIComponent(options.message) });
    }
  },

  // 跳转到佣金明细页面
  goToCommission() {
    wx.redirectTo({
      url: '/pages/attendant/commission/index',
      success: () => {
        console.log('成功跳转到佣金明细页面');
      },
      fail: (err) => {
        console.error('跳转佣金明细页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  // 返回上一页
  goBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      // 如果没有上一页，跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
    }
  },

  // 联系客服
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '如有疑问，请联系客服咨询。',
      confirmText: '联系客服',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 这里可以跳转到客服页面或拨打客服电话
          wx.showToast({
            title: '客服功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '陪诊师功能升级通知',
      path: '/pages/index/index'
    };
  }
});