.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
  padding: 40rpx 30rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 顶部区域 */
.header-section {
  text-align: center;
  padding: 60rpx 0;
}

.icon-wrapper {
  margin-bottom: 40rpx;
}

.upgrade-icon {
  width: 120rpx;
  height: 120rpx;
}

.title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.message {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 内容区域 */
.content-section {
  flex: 1;
}

.description-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.description {
  font-size: 28rpx;
  color: #555;
  line-height: 1.6;
}

/* 功能特点卡片 */
.features-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.features-title {
  margin-bottom: 30rpx;
}

.features-title text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.feature-icon {
  width: 32rpx;
  height: 32rpx;
  background: #07c160;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.feature-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.5;
  flex: 1;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.primary-btn {
  height: 88rpx;
  background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(7, 193, 96, 0.3);
  transition: all 0.3s ease;
}

.primary-btn::after {
  border: none;
}

.primary-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.2);
}

.secondary-btn {
  height: 88rpx;
  background: #fff;
  color: #07c160;
  font-size: 28rpx;
  font-weight: 500;
  border: 2rpx solid #07c160;
  border-radius: 44rpx;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.secondary-btn::after {
  border: none;
}

.secondary-btn:active {
  background: #f0f9ff;
  transform: scale(0.98);
}

/* 返回按钮区域 */
.back-section {
  text-align: center;
}

.back-btn {
  height: 72rpx;
  background: transparent;
  color: #999;
  font-size: 28rpx;
  border: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.back-btn::after {
  border: none;
}

.back-btn:active {
  color: #666;
  transform: scale(0.95);
}