<view class="container">
  <!-- 个人信息卡片 -->
  <view class="info-card">
    <view class="avatar-section">
      <image class="avatar" src="{{attendant.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="info">
        <text class="name">{{attendant.name}}</text>
        <view class="rating">
          <text class="rating-text">{{attendant.rating}}分</text>
          <text class="order-count">已服务{{attendant.orderCount}}人</text>
        </view>
      </view>
    </view>
    <view class="stats">
      <view class="stat-item">
        <text class="value">{{attendant.todayOrders}}</text>
        <text class="label">今日订单</text>
      </view>
      <view class="stat-item">
        <text class="value">¥{{attendant.todayIncome}}</text>
        <text class="label">今日收入</text>
      </view>
      <view class="stat-item">
        <text class="value">¥{{attendant.totalIncome}}</text>
        <text class="label">总收入</text>
      </view>
    </view>
  </view>

  <!-- 今日工作概览 -->
  <view class="workbench-orders">
    <view class="section-header">
      <view class="title-area">
        <text class="title">今日待处理订单</text>
        <text class="badge" wx:if="{{pendingOrderCount > 0}}">{{pendingOrderCount}}</text>
      </view>
      <text class="more-btn" bindtap="goToAllOrders">查看全部 ></text>
    </view>
    
    <!-- 快速统计 -->
    <view class="quick-stats">
      <view class="stat-item">
        <text class="number">{{todayStats.pending || 0}}</text>
        <text class="desc">待处理</text>
      </view>
      <view class="stat-item">
        <text class="number">{{todayStats.processing || 0}}</text>
        <text class="desc">进行中</text>
      </view>
      <view class="stat-item">
        <text class="number">{{todayStats.completed || 0}}</text>
        <text class="desc">已完成</text>
      </view>
    </view>

    <!-- 今日重要订单列表（最多显示5条） -->
    <view class="urgent-orders">
      <view class="order-summary" wx:for="{{urgentOrders}}" wx:key="id" bindtap="viewOrderDetail" data-id="{{item.id}}">
        <view class="order-header">
          <text class="patient-name">{{item.patientName}}</text>
          <text class="order-status {{item.status}}">{{item.statusText}}</text>
        </view>
        <view class="order-brief">
          <view class="info-row">
            <text class="hospital">{{item.hospital}}</text>
            <text class="time">{{item.date}} {{item.time}}</text>
          </view>
          <view class="info-row">
            <text class="department">{{item.department}}</text>
            <text class="order-no">{{item.orderNo || ('ORD-' + item.id)}}</text>
          </view>
        </view>
        <view class="order-actions" wx:if="{{item.status === 'pending' || item.status === 'confirmed' || item.status === 'in_progress'}}">
          <block wx:if="{{item.status === 'pending'}}">
            <button class="btn confirm-btn" bindtap="confirmOrder" data-id="{{item.id}}" catchtap>确认接单</button>
          </block>
          <block wx:if="{{item.status === 'confirmed'}}">
            <button class="btn start-btn" bindtap="startService" data-id="{{item.id}}" catchtap>开始服务</button>
          </block>
          <block wx:if="{{item.status === 'in_progress'}}">
            <button class="btn complete-btn" bindtap="completeService" data-id="{{item.id}}" catchtap>完成服务</button>
          </block>
          <block wx:if="{{item.status === 'pending_review' || item.status === 'under_review' || item.status === 'review_passed' || item.status === 'settled' || item.status === 'completed_admin'}}">
            <view class="status-text completed">{{item.statusText}}</view>
          </block>
        </view>
      </view>
      
      <view class="empty-tip" wx:if="{{urgentOrders.length === 0}}">
        <image class="empty-icon" src="/assets/images/empty-order.png" mode="aspectFit"></image>
        <text class="empty-text">今日暂无待处理订单</text>
        <text class="empty-desc">休息一下，等待新的服务机会</text>
      </view>
      
      <!-- 如果有更多订单，显示查看更多按钮 -->
      <view class="view-more" wx:if="{{hasMoreOrders}}" bindtap="goToAllOrders">
        <text class="more-text">还有 {{totalOrderCount - urgentOrders.length}} 个订单</text>
        <text class="more-action">查看全部订单 ></text>
      </view>
    </view>
  </view>

  <!-- 快速功能入口 -->
  <view class="quick-actions">
    <view class="action-item" bindtap="goToAllOrders">
      <view class="action-icon">📋</view>
      <text class="action-text">订单管理</text>
    </view>
    <view class="action-item" bindtap="goToSchedule">
      <view class="action-icon">⏰</view>
      <text class="action-text">工作时间</text>
    </view>
    <view class="action-item" bindtap="goToIncome">
      <view class="action-icon">💰</view>
      <text class="action-text">佣金明细</text>
    </view>
    <view class="action-item" bindtap="goToProfile">
      <view class="action-icon">👤</view>
      <text class="action-text">个人资料</text>
    </view>
  </view>
</view> 