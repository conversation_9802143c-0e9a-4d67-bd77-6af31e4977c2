const { getAttendantProfile, getMatchingOrderCount, acceptMatchingOrder, rejectMatchingOrder, getAttendantMergedOrders, getMatchingOrderList, startOrder } = require('../../../services/attendant');
const { setNotificationPage } = require('../../../services/notification');
const { checkLogin, getToken } = require('../../../utils/storage');
const { createSafeDate } = require('../../../utils/safe-date');

Page({
  data: {
    attendant: {
      name: '',
      avatar: '',
      rating: 0,
      orderCount: 0,
      todayOrders: 0,
      totalIncome: 0,
      todayIncome: 0
    },
    // 紧急/重要订单列表（限制最多5条）
    urgentOrders: [],
    // 今日统计数据
    todayStats: {
      pending: 0,
      processing: 0,
      completed: 0
    },
    pendingOrderCount: 0,
    totalOrderCount: 0, // 总订单数量
    hasMoreOrders: false, // 是否有更多订单
    notifications: [], // 用于显示通知
    refreshInterval: null, // 轮询定时器
    apiStatus: {
      profileApi: true,
      ordersApi: true,
      pendingCountApi: true
    },
    loading: false,
    errorMsg: '',
    attendantInfo: {
      name: '',
      avatar: '',
      id: null
    }
  },

  onLoad() {
    console.log('[Workbench] 页面加载开始')

    // 设置初始状态
    this.setData({
      loading: true,
      errorMsg: ''
    })

    // 加载陪诊师信息
    this.loadAttendantInfo()

    // 加载今日订单
    this.loadTodayOrders()

    console.log('[Workbench] 页面初始化完成')
  },

  onUnload() {
    // 页面卸载时清除轮询
    this.stopPolling()
  },

  onHide() {
    // 页面隐藏时清除轮询
    this.stopPolling()
  },

  onShow() {
    // 页面显示时启动轮询
    this.loadTodayOrders()
    this.startPolling()
  },

  // 启动轮询
  startPolling() {
    // 先清除之前的轮询
    this.stopPolling()

    // 设置30秒自动刷新 - 使用模拟数据所以可以保持更频繁的检查
    this.data.refreshInterval = setInterval(() => {
      console.log('轮询检查订单...')
      this.checkNewOrders()
    }, 30000) // 30秒
  },

  // 停止轮询
  stopPolling() {
    if (this.data.refreshInterval) {
      clearInterval(this.data.refreshInterval)
      this.data.refreshInterval = null
    }
  },

  // 检查新订单
  checkNewOrders() {
    console.log('[Workbench] 检查新订单 - 使用真实API数据')

    // 获取待处理订单数量 - 现在这个函数获取真实数据
    getMatchingOrderCount()
      .then(res => {
        const countData = res.data || res
        const pendingCount = countData.pending_count || 0

        // 如果待处理订单数量大于当前记录的数量，说明有新订单
        if (pendingCount > this.data.pendingOrderCount) {
          console.log('发现新的待处理订单！', pendingCount, this.data.pendingOrderCount)

          // 更新订单列表
          this.loadTodayOrders()

          // 显示通知
          wx.showToast({
            title: '收到新的订单请求！',
            icon: 'success'
          })

          // 尝试播放提示音
          try {
            const innerAudioContext = wx.createInnerAudioContext()
            innerAudioContext.src = '/assets/sounds/notification.mp3'
            innerAudioContext.play()
          } catch (audioErr) {
            console.error('播放提示音失败:', audioErr)
          }
        }

        // 更新待处理订单数量
        this.setData({
          pendingOrderCount: pendingCount
        })
      })
      .catch(err => {
        console.error('获取待处理订单数量失败:', err)
      })
  },

  // 加载陪诊师信息
  loadAttendantInfo() {
    console.log('[Workbench] 开始加载陪诊师信息')

    // 先尝试从本地存储获取
    const cachedInfo = wx.getStorageSync('attendantInfo')
    if (cachedInfo && cachedInfo.id) {
      console.log('[Workbench] 使用缓存的陪诊师信息:', cachedInfo)
      this.setData({
        attendantInfo: {
          name: cachedInfo.name || '陪诊师',
          avatar: cachedInfo.avatar || '',
          id: cachedInfo.id
        },
        // 同时更新attendant对象用于头像显示
        attendant: {
          ...this.data.attendant,
          name: cachedInfo.name || '陪诊师',
          avatar: cachedInfo.avatar || ''
        }
      })
    }

    // 然后尝试从API获取最新信息
    getAttendantProfile()
      .then(res => {
        console.log('[Workbench] 获取陪诊师档案成功:', res)

        const profileData = res.data || res
        const attendantInfo = profileData.attendant || profileData

        if (attendantInfo && attendantInfo.id) {
          const info = {
            name: attendantInfo.name || attendantInfo.real_name || '陪诊师',
            avatar: attendantInfo.avatar || attendantInfo.avatar_url || '',
            id: attendantInfo.id,
            phone: attendantInfo.phone || '',
            rating: attendantInfo.rating || 0,
            completedOrders: attendantInfo.completed_orders || 0
          }

          // 更新陪诊师信息和头像显示
          this.setData({
            attendantInfo: info,
            // 同时更新attendant对象用于头像显示
            attendant: {
              ...this.data.attendant,
              name: info.name,
              avatar: info.avatar,
              rating: info.rating,
              orderCount: info.completedOrders
            }
          })

          // 更新本地缓存
          wx.setStorageSync('attendantInfo', info)

          console.log('[Workbench] 陪诊师信息更新完成，头像URL:', info.avatar)
        }
      })
      .catch(err => {
        console.error('[Workbench] 获取陪诊师档案失败:', err)

        // 如果没有缓存信息，设置默认信息
        if (!cachedInfo || !cachedInfo.id) {
          this.setData({
            attendantInfo: {
              name: '陪诊师',
              avatar: '',
              id: null
            },
            attendant: {
              ...this.data.attendant,
              name: '陪诊师',
              avatar: ''
            }
          })
        }

        // 不显示错误提示，避免影响用户体验
        console.log('[Workbench] 使用默认陪诊师信息')
      })
  },

  // 加载今日订单
  loadTodayOrders() {
    console.log('[Workbench] 开始加载今日订单')

    this.setData({ loading: true })

    // 使用陪诊师合并订单接口
    getAttendantMergedOrders()
      .then(res => {
        console.log('[Workbench] 获取陪诊师订单成功:', res)
        this.processTodayOrdersData(res)
      })
      .catch(err => {
        console.error('[Workbench] 获取陪诊师订单失败:', err)

        // 如果API失败，使用备用方法
        this.loadTodayOrdersBackup()
      })
  },

  // 处理今日订单数据
  processTodayOrdersData(res) {
    try {
      console.log('[Workbench] 开始处理订单数据:', res)

      const responseData = res.data || res
      const orderList = responseData.orders || responseData.list || responseData || []

      console.log('[Workbench] 原始订单列表:', orderList)

      // 获取今天的日期字符串
      const today = new Date()
      const todayStr = today.getFullYear() + '-' +
        String(today.getMonth() + 1).padStart(2, '0') + '-' +
        String(today.getDate()).padStart(2, '0')

      console.log('[Workbench] 今日日期:', todayStr)

      // 处理订单数据，提取今日订单
      const processedOrders = orderList.map(order => {
        const patientName = this.getPatientName(order)
        const hospital = this.getHospitalName(order)
        const department = this.getDepartmentName(order)
        const serviceTime = this.getServiceTime(order)
        const status = this.getOrderStatus(order)
        const statusText = this.getOrderStatusText(order)

        console.log(`[Workbench] 处理订单 ${order.id || order.ID}:`, {
          患者: patientName,
          医院: hospital,
          科室: department,
          时间: serviceTime,
          状态: status,
          状态文本: statusText
        })

        return {
          id: order.id || order.ID,
          ID: order.id || order.ID,
          patientName: patientName,
          hospital: hospital,
          department: department,
          serviceTime: serviceTime,
          date: this.formatDate(serviceTime),
          time: this.formatTime(serviceTime),
          status: status,
          statusText: statusText,
          amount: order.amount || order.Amount || 0,
          type: order.type || 'order',
          orderNo: this.getOrderNo(order)
        }
      })

      console.log('[Workbench] 处理后的订单数据:', processedOrders)

      // 统计不同状态的订单数量
      const todayCount = processedOrders.length
      const pendingCount = processedOrders.filter(order =>
        order.status === 'pending' || order.status === 'confirmed'
      ).length
      const inProgressCount = processedOrders.filter(order =>
        order.status === 'in_progress'
      ).length
      const completedCount = processedOrders.filter(order =>
        order.status === 'completed'
      ).length
      const manualHandlingCount = processedOrders.filter(order =>
        order.status === 'manual_handling'
      ).length

      // 计算今日收入（已完成订单的金额总和）
      const todayIncome = processedOrders
        .filter(order => order.status === 'completed')
        .reduce((sum, order) => sum + (order.amount || 0), 0)

      // 计算总收入（所有已完成订单，这里简化为今日收入，实际应该调用专门的收入统计接口）
      const totalIncome = todayIncome

      // 更新页面数据
      this.setData({
        urgentOrders: processedOrders,
        todayOrderCount: todayCount,
        pendingOrderCount: pendingCount,
        inProgressOrderCount: inProgressCount,
        completedOrderCount: completedCount,
        // 更新今日统计数据
        todayStats: {
          pending: pendingCount,
          processing: inProgressCount,
          completed: completedCount
        },
        // 更新顶部区域的统计数据
        attendant: {
          ...this.data.attendant,
          todayOrders: todayCount,
          todayIncome: todayIncome,
          totalIncome: totalIncome
        },
        loading: false,
        errorMsg: ''
      })

      console.log('[Workbench] 订单数据更新完成，统计信息:', {
        总数: todayCount,
        待处理: pendingCount,
        进行中: inProgressCount,
        已完成: completedCount,
        今日收入: todayIncome,
        总收入: totalIncome
      })

    } catch (error) {
      console.error('[Workbench] 处理订单数据时发生错误:', error)

      // 设置错误状态
      this.setData({
        loading: false,
        errorMsg: '数据处理失败，请重试'
      })

      // 显示错误提示
      wx.showToast({
        title: '数据处理失败',
        icon: 'none'
      })
    }
  },

  // 备用方法：加载今日订单（当主API失败时使用）
  loadTodayOrdersBackup() {
    console.log('[Workbench] 使用备用方法加载今日订单')

    // 设置模拟数据，避免页面空白
    const mockOrders = [
      {
        id: 10000,
        ID: 10000,
        orderNo: 'ORD202506141010406329',
        patientName: '梅长苏',
        hospital: '北京协和医院',
        department: '内科',
        serviceTime: '2025-06-15 00:00',
        status: 'completed',
        statusText: '已完成',
        amount: 150,
        type: 'order'
      }
    ]

    // 更新页面数据
    this.setData({
      urgentOrders: mockOrders,
      todayOrderCount: mockOrders.length,
      pendingOrderCount: 0,
      inProgressOrderCount: 0,
      completedOrderCount: 1,
      // 更新今日统计数据
      todayStats: {
        pending: 0,
        processing: 0,
        completed: 1
      },
      // 更新顶部区域的统计数据
      attendant: {
        ...this.data.attendant,
        todayOrders: 1,
        todayIncome: 150,
        totalIncome: 150
      },
      loading: false,
      errorMsg: ''
    })

    console.log('[Workbench] 备用数据加载完成')
  },

  // 查看全部订单 - 优化后的跳转逻辑
  goToAllOrders() {
    console.log('点击查看全部订单')

    // 埋点统计
    wx.reportEvent('workbench_to_orders_click', {
      from: 'workbench',
      urgent_count: this.data.urgentOrders.length,
      total_count: this.data.totalOrderCount
    })

    wx.showLoading({
      title: '正在加载订单管理中心...',
      mask: true
    })

    setTimeout(() => {
      wx.hideLoading()
      wx.navigateTo({
        url: '/pages/attendant/orders/index',
        success: () => {
          console.log('从工作台跳转到订单管理成功')
          // 显示提示，说明从工作台跳转过来
          setTimeout(() => {
            wx.showToast({
              title: '这里可以查看完整订单历史',
              icon: 'none',
              duration: 2000
            })
          }, 800)
        },
        fail: (err) => {
          console.error('跳转到订单管理失败:', err)
          wx.showModal({
            title: '提示',
            content: '订单管理页面暂时无法访问，请稍后重试',
            showCancel: false,
            confirmText: '我知道了'
          })
        }
      })
    }, 300)
  },

  // 快速功能入口跳转方法
  goToSchedule() {
    wx.navigateTo({
      url: '/pages/attendant/schedule/index'
    })
  },

  goToIncome() {
    wx.navigateTo({
      url: '/pages/attendant/commission/index'
    })
  },

  goToProfile() {
    // 获取当前陪诊师ID
    const attendantInfo = wx.getStorageSync('attendantInfo');
    const userInfo = wx.getStorageSync('userInfo');

    // 尝试从多个来源获取陪诊师ID
    let attendantId = null;

    // 首先从attendantInfo中获取
    if (attendantInfo && attendantInfo.id) {
      attendantId = attendantInfo.id;
      console.log('[Workbench] 从attendantInfo获取到陪诊师ID:', attendantId);
    }
    // 如果没有，从this.data.attendant获取
    else if (this.data.attendant && this.data.attendant.id) {
      attendantId = this.data.attendant.id;
      console.log('[Workbench] 从页面数据获取到陪诊师ID:', attendantId);

      // 顺便更新存储
      wx.setStorageSync('attendantInfo', {
        id: attendantId,
        name: this.data.attendant.name || '陪诊师',
        avatar: this.data.attendant.avatar || ''
      });
    }
    // 最后从userInfo尝试获取
    else if (userInfo && userInfo.attendant_id) {
      attendantId = userInfo.attendant_id;
      console.log('[Workbench] 从userInfo获取到陪诊师ID:', attendantId);

      // 顺便更新存储
      wx.setStorageSync('attendantInfo', {
        id: attendantId,
        name: userInfo.name || userInfo.nickname || '陪诊师',
        avatar: userInfo.avatar || userInfo.avatarUrl || ''
      });
    }

    console.log('[Workbench] 跳转到个人资料页面，最终使用ID:', attendantId);

    if (!attendantId) {
      console.error('[Workbench] 无法获取陪诊师ID');

      // 显示错误提示并返回，不使用硬编码ID
      wx.showToast({
        title: '获取陪诊师信息失败，请重新登录',
        icon: 'none'
      });

      // 跳转到登录页面
      setTimeout(() => {
        wx.redirectTo({
          url: '/pages/login/index'
        })
      }, 2000)
      return
    }

    // 打印完整URL用于调试
    const url = `/pages/attendant/profile/index?id=${attendantId}`;
    console.log('[Workbench] 跳转URL:', url);

    // 跳转前记录日志
    console.log('[Workbench] 即将跳转到个人资料页面');

    wx.navigateTo({
      url: url,
      success: (res) => {
        console.log('[Workbench] 成功跳转到个人资料页面');
      },
      fail: (err) => {
        console.error('[Workbench] 跳转到个人资料页面失败:', err);

        // 尝试备用方式跳转
        wx.navigateTo({
          url: `/pages/attendant/profile/index`,
          success: () => {
            console.log('[Workbench] 备用方式跳转成功');
          },
          fail: (e) => {
            console.error('[Workbench] 备用跳转也失败:', e);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  // 辅助方法：从不同格式的数据中获取患者姓名
  getPatientName(order) {
    console.log('[Workbench] 获取患者姓名，订单数据:', order)

    // 直接字段名（优先级最高）
    if (order && order.patient_name) {
      return order.patient_name
    } else if (order && order.PatientName) {
      return order.PatientName
    }
    // 处理嵌套的患者信息
    else if (order && order.Patient && order.Patient.Name) {
      return order.Patient.Name
    } else if (order && order.patient && order.patient.name) {
      return order.patient.name
    } else if (order && order.Patient && order.Patient.name) {
      return order.Patient.name
    } else if (order && order.order && order.order.patient_name) {
      return order.order.patient_name
    } else if (order && order.order && order.order.PatientName) {
      return order.order.PatientName
    } else if (order && order.order && order.order.patient && order.order.patient.name) {
      return order.order.patient.name
    } else if (order && order.order && order.order.Patient && order.order.Patient.Name) {
      return order.order.Patient.Name
    } else if (order && order.order && order.order.Patient && order.order.Patient.name) {
      return order.order.Patient.name
    } else {
      return '未知患者'
    }
  },

  // 辅助方法：从不同格式的数据中获取医院名称
  getHospitalName(order) {
    console.log('[Workbench] 获取医院名称，订单数据:', order)

    // 直接字段名
    if (order && order.hospital_name) {
      return order.hospital_name
    } else if (order && order.HospitalName) {
      return order.HospitalName
    }
    // 处理嵌套的医院信息
    else if (order && order.Hospital && order.Hospital.Name) {
      return order.Hospital.Name
    } else if (order && order.hospital && order.hospital.name) {
      return order.hospital.name
    } else if (order && order.order && order.order.hospital && order.order.hospital.name) {
      return order.order.hospital.name
    } else if (order && (order.Hospital || order.hospital)) {
      return order.Hospital || order.hospital
    } else {
      return '未指定医院'
    }
  },

  // 辅助方法：从不同格式的数据中获取科室名称
  getDepartmentName(order) {
    console.log('[Workbench] 获取科室名称，订单数据:', order)

    // 直接字段名
    if (order && order.department_name) {
      return order.department_name
    } else if (order && order.DepartmentName) {
      return order.DepartmentName
    }
    // 处理嵌套的科室信息
    else if (order && order.Department && order.Department.Name) {
      return order.Department.Name
    } else if (order && order.department && order.department.name) {
      return order.department.name
    } else if (order && order.order && order.order.department && order.order.department.name) {
      return order.order.department.name
    } else if (order && (order.Department || order.department)) {
      return order.Department || order.department
    } else {
      return '未指定科室'
    }
  },

  // 辅助方法：从不同格式的数据中获取服务时间
  getServiceTime(order) {
    console.log('[Workbench] 获取服务时间，订单数据:', order)
    
    // 直接字段名
    if (order && order.appointment_time) {
      return order.appointment_time
    } else if (order && order.service_time) {
      return order.service_time
    }
    // 处理嵌套的服务时间信息
    else if (order && order.ServiceTime) {
      return order.ServiceTime
    } else if (order && order.order && order.order.appointment_time) {
      return order.order.appointment_time
    } else if (order && order.order && order.order.service_time) {
      return order.order.service_time
    } else {
      return ''
    }
  },

    // 辅助方法：从不同格式的数据中获取订单状态
    getOrderStatus(order) {
    console.log('[Workbench] 获取订单状态，原始数据:', order)
    console.log('[Workbench] 订单数据类型:', typeof order)
    console.log('[Workbench] 订单数据键:', Object.keys(order || {}))

    if (!order) return 'pending'

    // 处理后端返回的状态值
    let status = order.Status || order.status || order.order_status
    let matchingStatus = order.MatchingStatus || order.matching_status

    console.log('[Workbench] 状态值详细信息:')
    console.log('  - order.Status:', order.Status)
    console.log('  - order.status:', order.status)
    console.log('  - order.order_status:', order.order_status)
    console.log('  - order.MatchingStatus:', order.MatchingStatus)
    console.log('  - order.matching_status:', order.matching_status)
    console.log('  - 最终status:', status, '类型:', typeof status)
    console.log('  - 最终matchingStatus:', matchingStatus, '类型:', typeof matchingStatus)

    // 优先根据订单状态判断（status字段优先级最高）
    // status === 4 已废弃，统一使用T+2审核流程
    if (status === 8) {
      return 'pending_review'  // 已完成-待审核
    } else if (status === 9) {
      return 'under_review'  // 审核中
    } else if (status === 10) {
      return 'review_passed'  // 审核通过
    } else if (status === 11) {
      return 'review_failed'  // 审核不通过
    } else if (status === 13) {
      return 'settled'  // 已结算
    }
    
    // 根据匹配状态判断（次优先级）
    if (matchingStatus === 1) {
      // 匹配状态为1表示待接单
      return 'pending'
    } else if (matchingStatus === 2) {
      return 'confirmed'  // 已匹配，待开始服务
    } else if (matchingStatus === 4 || matchingStatus === 6) {
      return 'in_progress' // 服务进行中 (人工处理中或服务已开始)
    } else if (matchingStatus === 0 || !matchingStatus) {
      // 匹配状态为0或空，表示还未进入匹配流程，应该显示为待接单
      return 'pending'    // 待接单
    } else {
      return 'in_progress' // 默认服务进行中
    }
  },

  // 获取订单状态文本
  getOrderStatusText(order) {
    const status = this.getOrderStatus(order)
    const statusMap = {
      'created': '待支付',
      'matching': '匹配中',
      'pending': '待接单',
      'confirmed': '待服务',
      'in_progress': '进行中',
      'completed': '已完成',
      'completed_admin': '已完成(管理员)', // 管理员强制完成
      'cancelled': '已取消',
      'pending_refund': '待退款',
      'manual_handling': '匹配中', // 陪诊师看到的是匹配中状态
      'pending_review': '待审核',
      'under_review': '审核中',
      'review_passed': '审核通过',
      'review_failed': '审核不通过',
      'settled': '已结算'
    }
    return statusMap[status] || '未知状态'
  },

  // 辅助方法：从不同格式的数据中获取订单编号
  getOrderNo(order) {
    if (!order) return 'ORD-' + Date.now()

    if (order.OrderNo) {
      return order.OrderNo
    } else if (order.AppointmentNo) {
      return order.AppointmentNo
    } else if (order.order && order.order.order_no) {
      return order.order.order_no
    } else if (order.order_no) {
      return order.order_no
    } else if (order.orderNo) {
      return order.orderNo
    } else {
      return 'ORD-' + (order.ID || order.id || Date.now())
    }
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '未知日期'
    const date = createSafeDate(dateString)
    return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
  },

  // 格式化时间
  formatTime(dateString) {
    if (!dateString) return '未知时间'
    const date = createSafeDate(dateString)
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const { id } = e.currentTarget.dataset
    console.log('[Workbench] 查看订单详情，点击的ID:', id)

    const orderIndex = this.data.urgentOrders.findIndex(item =>
      item.id === id || item.ID === id || item.order_id === id
    )

    if (orderIndex === -1) {
      console.error('[Workbench] 找不到订单信息，id:', id, '订单列表:', this.data.urgentOrders)
      wx.showToast({
        title: '找不到订单信息',
        icon: 'none'
      })
      return
    }

    // 获取订单信息
    const order = this.data.urgentOrders[orderIndex]
    console.log('[Workbench] 查看订单详情，订单信息:', order)

    // 获取正确的订单ID
    const orderId = order.ID || order.id || order.orderId || order.order_id
    const orderType = order.type || 'order'

    if (!orderId) {
      console.error('[Workbench] 订单ID无效:', order)
      wx.showToast({
        title: '订单ID无效',
        icon: 'none'
      })
      return
    }

    console.log('[Workbench] 跳转到订单详情，ID:', orderId, 'Type:', orderType)

    // 跳转到订单详情页面
    wx.navigateTo({
      url: `/pages/attendant/orders/detail/index?id=${orderId}&type=${orderType}`,
      success: () => {
        console.log('[Workbench] 成功跳转到订单详情页面')
      },
      fail: (err) => {
        console.error('[Workbench] 跳转到订单详情页面失败:', err)

        // 如果页面栈满了，使用redirectTo
        if (err.errMsg && err.errMsg.includes('limit exceed')) {
          console.log('[Workbench] 页面栈已满，使用redirectTo跳转')
          wx.redirectTo({
            url: `/pages/attendant/orders/detail/index?id=${orderId}&type=${orderType}`,
            success: () => {
              console.log('[Workbench] redirectTo跳转成功')
            },
            fail: (redirectErr) => {
              console.error('[Workbench] redirectTo跳转也失败:', redirectErr)
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              })
            }
          })
        } else {
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
        }
      }
    })
  },

  // 确认接单
  confirmOrder(e) {
    const id = e.currentTarget.dataset.id
    console.log('[Workbench] 确认接单，点击的ID:', id)

    const orderIndex = this.data.urgentOrders.findIndex(item =>
      item.id === id || item.ID === id || item.order_id === id
    )

    if (orderIndex === -1) {
      console.error('[Workbench] 找不到订单信息，id:', id, '订单列表:', this.data.urgentOrders)
      wx.showToast({
        title: '找不到订单信息',
        icon: 'none'
      })
      return
    }

    const order = this.data.urgentOrders[orderIndex]
    const orderId = order.ID || order.id || order.orderId || order.order_id

    if (!orderId) {
      console.error('[Workbench] 订单ID无效:', order)
      wx.showToast({
        title: '订单ID无效',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '确认接单',
      content: '接受该订单后，您需要在约定时间提供陪诊服务',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中'
          })

          acceptMatchingOrder(orderId)
            .then(res => {
              wx.hideLoading()
              console.log('[Workbench] 接单API响应:', res)

              // 更宽松的成功判断条件
              if (res.code === 0 || res.success || (res.data && res.data.success)) {
                wx.showToast({
                  title: '接单成功',
                  icon: 'success'
                })
                // 延迟重新加载今日订单
                setTimeout(() => {
                  this.loadTodayOrders()
                }, 1000)
              } else {
                wx.showToast({
                  title: res.message || '接单失败',
                  icon: 'none'
                })
              }
            })
            .catch(err => {
              wx.hideLoading()
              console.error('[Workbench] 接单失败:', err)

              // 如果是网络错误或404，显示友好提示
              if (err.code === 404 || (err.message && err.message.includes('404'))) {
                wx.showToast({
                  title: '功能暂未开放，请稍后再试',
                  icon: 'none'
                })
              } else {
                wx.showToast({
                  title: err.message || '接单失败',
                  icon: 'none'
                })
              }
            })
        }
      }
    })
  },

  // 开始服务
  startService(e) {
    const { id } = e.currentTarget.dataset
    console.log('[Workbench] 开始服务，点击的ID:', id)

    const orderIndex = this.data.urgentOrders.findIndex(item =>
      item.id === id || item.ID === id || item.order_id === id
    )

    if (orderIndex === -1) {
      console.error('[Workbench] 找不到订单信息，id:', id, '订单列表:', this.data.urgentOrders)
      wx.showToast({
        title: '找不到订单信息',
        icon: 'none'
      })
      return
    }

    const order = this.data.urgentOrders[orderIndex]
    const orderId = order.ID || order.id || order.orderId || order.order_id

    console.log('[Workbench] 开始服务，订单ID:', orderId, '订单信息:', order)

    if (!orderId) {
      console.error('[Workbench] 订单ID无效:', order)
      wx.showToast({
        title: '订单ID无效',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '开始服务',
      content: '确认开始此次服务吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中'
          })

          // 调用开始服务的API
          startOrder(orderId)
            .then(res => {
              wx.hideLoading()
              console.log('[Workbench] 开始服务API响应:', res)

              // 更宽松的成功判断条件
              if (res.code === 0 || res.success || (res.data && res.data.success) || res.message === '服务已开始') {
                wx.showToast({
                  title: '已开始服务',
                  icon: 'success'
                })
                // 延迟重新加载，确保后端状态更新
                setTimeout(() => {
                  this.loadTodayOrders()
                }, 1000)
              } else {
                wx.showToast({
                  title: res.message || '开始服务失败',
                  icon: 'none'
                })
              }
            })
            .catch(err => {
              wx.hideLoading()
              console.error('[Workbench] 开始服务失败:', err)

              // 如果是网络错误或404，显示友好提示
              if (err.code === 404 || (err.message && err.message.includes('404'))) {
                wx.showToast({
                  title: '功能暂未开放，请稍后再试',
                  icon: 'none'
                })
              } else {
                wx.showToast({
                  title: err.message || '开始服务失败',
                  icon: 'none'
                })
              }
            })
        }
      }
    })
  },

  // 完成服务
  completeService(e) {
    const { id } = e.currentTarget.dataset
    console.log('[Workbench] 完成服务，点击的ID:', id)

    const orderIndex = this.data.urgentOrders.findIndex(item => item.id === id)

    if (orderIndex === -1) {
      console.error('[Workbench] 找不到订单信息，id:', id, '订单列表:', this.data.urgentOrders)
      wx.showToast({
        title: '找不到订单信息',
        icon: 'none'
      })
      return
    }

    const order = this.data.urgentOrders[orderIndex]
    console.log('[Workbench] 完成服务，订单信息:', order)

    // 获取正确的订单ID，使用与其他方法相同的逻辑
    const orderId = order.ID || order.id || order.orderId || order.order_id

    if (!orderId) {
      console.error('[Workbench] 订单ID无效:', order)
      wx.showToast({
        title: '订单ID无效',
        icon: 'none'
      })
      return
    }

    console.log('[Workbench] 完成服务，使用的订单ID:', orderId)

    wx.showModal({
      title: '完成服务',
      content: '为确保服务质量，完成服务需要上传病例照片等证据材料',
      confirmText: '去完成',
      showCancel: false,
      success: (res) => {
        if (res.confirm) {
          console.log('[Workbench] 准备跳转到完成服务页面，URL:', `/pages/attendant/orders/complete/index?orderId=${orderId}`)

          // 检查页面栈数量
          const pages = getCurrentPages();
          console.log('[Workbench] 当前页面栈数量:', pages.length);

          if (pages.length >= 10) {
            console.warn('[Workbench] 页面栈已满，使用redirectTo跳转');
            wx.redirectTo({
              url: `/pages/attendant/orders/complete/index?orderId=${orderId}`,
              success: () => {
                console.log('[Workbench] redirectTo跳转到完成服务页面成功')
              },
              fail: (err) => {
                console.error('[Workbench] redirectTo跳转到完成服务页面失败:', err)
                wx.showToast({
                  title: '页面跳转失败: ' + (err.errMsg || '未知错误'),
                  icon: 'none'
                })
              }
            });
          } else {
            // 跳转到完成服务页面（必须上传证据）
            wx.navigateTo({
              url: `/pages/attendant/orders/complete/index?orderId=${orderId}`,
              success: () => {
                console.log('[Workbench] navigateTo跳转到完成服务页面成功')
              },
              fail: (err) => {
                console.error('[Workbench] navigateTo跳转到完成服务页面失败:', err)

                // 如果navigateTo失败，尝试使用redirectTo
                console.log('[Workbench] 尝试使用redirectTo作为备选方案');
                wx.redirectTo({
                  url: `/pages/attendant/orders/complete/index?orderId=${orderId}`,
                  success: () => {
                    console.log('[Workbench] 备选redirectTo跳转成功')
                  },
                  fail: (redirectErr) => {
                    console.error('[Workbench] 备选redirectTo也失败:', redirectErr)
                    wx.showToast({
                      title: '页面跳转失败: ' + (redirectErr.errMsg || '未知错误'),
                      icon: 'none'
                    })
                  }
                });
              }
            });
          }
        }
      }
    })
  }
}) 